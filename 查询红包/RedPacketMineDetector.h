#import <Foundation/Foundation.h>

@interface RedPacketMineDetector : NSObject

/**
 * 智能识别红包扫雷游戏的雷值
 * @param amount 红包金额（支持小数，如0.1、1.5等）
 * @param message 祝福语内容
 * @return 识别出的雷值字符串，如果识别失败返回nil
 */
+ (NSString *)detectMineValueWithAmount:(CGFloat)amount message:(NSString *)message;

/**
 * 标准化文本并提取纯数字
 * @param text 输入文本
 * @return 标准化后的纯数字字符串
 */
+ (NSString *)normalizeAndExtractNumbers:(NSString *)text;

/**
 * 标准化文本，将容易混淆的字符转换为对应数字
 * @param text 输入文本
 * @return 标准化后的文本
 */
+ (NSString *)normalizeText:(NSString *)text;

/**
 * 从字符串中提取所有数字
 * @param text 输入文本
 * @return 提取出的数字字符串
 */
+ (NSString *)extractNumbersFromText:(NSString *)text;

/**
 * 验证雷值是否有效
 * @param mineValue 雷值
 * @param amount 红包金额（支持小数）
 * @return 是否有效
 */
+ (BOOL)isValidMineValue:(NSString *)mineValue forAmount:(CGFloat)amount;

@end

