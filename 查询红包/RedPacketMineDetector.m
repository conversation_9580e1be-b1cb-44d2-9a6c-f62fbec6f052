#import "RedPacketMineDetector.h"

@implementation RedPacketMineDetector

+ (NSString *)detectMineValueWithAmount:(CGFloat)amount message:(NSString *)message {
    if (!message || message.length == 0) {
        return nil;
    }
    
    // 清理输入，移除前后空格
    NSString *cleanMessage = [message stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    
    // 如果cleanMessage 0开头，直接调用规则4
    if (cleanMessage.length > 0 && [cleanMessage characterAtIndex:0] == '0') {
        // 规则4：祝福语直接是雷值（纯数字）
        NSString *rule4Result = [self detectRule4:cleanMessage amount:amount];
        if (rule4Result) {
            // 确保返回结果也经过标准化处理，去除可能的混淆字符
            return [self normalizeAndExtractNumbers:rule4Result];
        }
    } else {
        // 规则1：金额+雷值的组合（优先级最高）
        NSString *rule1Result = [self detectRule1:cleanMessage amount:amount];
        if (rule1Result) {
            // 确保返回结果也经过标准化处理，去除可能的混淆字符
            return [self normalizeAndExtractNumbers:rule1Result];
        }
        
        // 规则2：金额+特殊符号/字符/中文+雷值
        NSString *rule2Result = [self detectRule2:cleanMessage amount:amount];
        if (rule2Result) {
            // 确保返回结果也经过标准化处理，去除可能的混淆字符
            return [self normalizeAndExtractNumbers:rule2Result];
        }
        
        // 规则3：雷值被特殊符号/字符分隔
        NSString *rule3Result = [self detectRule3:cleanMessage amount:amount];
        if (rule3Result) {
            // 确保返回结果也经过标准化处理，去除可能的混淆字符
            return [self normalizeAndExtractNumbers:rule3Result];
        }
        
        // 规则4：祝福语直接是雷值（纯数字）
        NSString *rule4Result = [self detectRule4:cleanMessage amount:amount];
        if (rule4Result) {
            // 确保返回结果也经过标准化处理，去除可能的混淆字符
            return [self normalizeAndExtractNumbers:rule4Result];
        }
    }
    
    return nil;
}

#pragma mark - 规则1：金额+雷值组合
+ (NSString *)detectRule1:(NSString *)message amount:(CGFloat)amount {
    // 提取所有数字
    NSString *numbers = [self extractNumbersFromText:message];
    if (numbers.length == 0) {
        return nil;
    }
    
    // 将浮点数金额转换为字符串，去除小数点
    NSString *amountStr;
    if (amount == (NSInteger)amount) {
        // 整数金额
        amountStr = [NSString stringWithFormat:@"%.0f", amount];
    } else {
        // 小数金额，转换为分（乘以100）
        NSInteger amountInCents = (NSInteger)(amount * 100);
        amountStr = [NSString stringWithFormat:@"%ld", (long)amountInCents];
    }
    
    // 检查是否以金额开头
    if ([numbers hasPrefix:amountStr]) {
        NSString *remainingNumbers = [numbers substringFromIndex:amountStr.length];
        if (remainingNumbers.length > 0) {
            // 验证雷值的有效性
            if ([self isValidMineValue:remainingNumbers forAmount:amount]) {
                return remainingNumbers;
            }
        }
    }
    
    return nil;
}

#pragma mark - 规则2：金额+特殊符号+雷值
+ (NSString *)detectRule2:(NSString *)message amount:(CGFloat)amount {
    // 将浮点数金额转换为字符串
    NSString *amountStr;
    if (amount == (NSInteger)amount) {
        // 整数金额
        amountStr = [NSString stringWithFormat:@"%.0f", amount];
    } else {
        // 小数金额，同时尝试原始格式和分格式
        NSString *originalFormat = [NSString stringWithFormat:@"%.1f", amount];
        NSInteger amountInCents = (NSInteger)(amount * 100);
        NSString *centsFormat = [NSString stringWithFormat:@"%ld", (long)amountInCents];
        
        // 先尝试原始格式（如"0.1"）
        NSRange originalRange = [message rangeOfString:originalFormat];
        if (originalRange.location != NSNotFound) {
            amountStr = originalFormat;
        } else {
            // 再尝试分格式（如"10"）
            amountStr = centsFormat;
        }
    }
    
    // 查找金额在消息中的位置
    NSRange amountRange = [message rangeOfString:amountStr];
    if (amountRange.location == NSNotFound) {
        return nil;
    }
    
    // 获取金额后面的部分
    NSUInteger startIndex = amountRange.location + amountRange.length;
    if (startIndex >= message.length) {
        return nil;
    }
    
    NSString *afterAmount = [message substringFromIndex:startIndex];
    
    // 从金额后面的部分提取数字（跳过特殊符号）
    NSString *mineValue = [self extractNumbersFromText:afterAmount];
    
    if (mineValue.length > 0 && [self isValidMineValue:mineValue forAmount:amount]) {
        return mineValue;
    }
    
    return nil;
}

#pragma mark - 规则3：雷值被特殊符号分隔
+ (NSString *)detectRule3:(NSString *)message amount:(CGFloat)amount {
    // 提取所有数字字符
    NSMutableString *extractedNumbers = [NSMutableString string];
    
    for (NSUInteger i = 0; i < message.length; i++) {
        unichar character = [message characterAtIndex:i];
        if (character >= '0' && character <= '9') {
            [extractedNumbers appendFormat:@"%C", character];
        }
    }
    
    if (extractedNumbers.length == 0) {
        return nil;
    }
    
    // 验证提取的数字是否为有效雷值
    if ([self isValidMineValue:extractedNumbers forAmount:amount]) {
        return extractedNumbers;
    }
    
    return nil;
}

#pragma mark - 规则4：祝福语直接是雷值
+ (NSString *)detectRule4:(NSString *)message amount:(CGFloat)amount {
    // 检查是否为纯数字
    NSString *numbers = [self extractNumbersFromText:message];
    
    // 如果提取的数字和原消息长度相同，说明是纯数字
    if (numbers.length == message.length && numbers.length > 0) {
        if ([self isValidMineValue:numbers forAmount:amount]) {
            return numbers;
        }
    }
    // 即使不是纯数字，也尝试验证提取出的数字是否为有效雷值
    else if (numbers.length > 0) {
        if ([self isValidMineValue:numbers forAmount:amount]) {
            return numbers;
        }
    }
    
    return nil;
}

#pragma mark - 辅助方法

+ (NSString *)normalizeAndExtractNumbers:(NSString *)text {
    if (!text) {
        return nil;
    }
    
    // 先标准化文本，将混淆字符转换为数字
    NSString *normalizedText = [self normalizeText:text];
    
    // 然后提取纯数字
    return [self extractNumbersFromText:normalizedText];
}

+ (NSString *)normalizeText:(NSString *)text {
    if (!text) {
        return @"";
    }
    
    NSMutableString *normalized = [text mutableCopy];
    
    // 字符映射表：容易混淆的字符 -> 对应数字
    NSDictionary *charMap = @{
        @"o": @"0",  // 小写字母o -> 0
        @"O": @"0",  // 大写字母O -> 0
        @"l": @"1",  // 小写字母l -> 1
        @"I": @"1",  // 大写字母I -> 1
        @"S": @"5",  // 大写字母S -> 5
        @"s": @"5",  // 小写字母s -> 5
        @"Z": @"2",  // 大写字母Z -> 2
        @"z": @"2",  // 小写字母z -> 2
    };
    
    // 替换容易混淆的字符
    for (NSString *key in charMap) {
        NSString *value = charMap[key];
        [normalized replaceOccurrencesOfString:key
                                    withString:value
                                       options:NSCaseInsensitiveSearch
                                         range:NSMakeRange(0, normalized.length)];
    }
    
    return normalized;
}

+ (NSString *)extractNumbersFromText:(NSString *)text {
    if (!text) {
        return @"";
    }
    
    // 首先标准化文本，将容易混淆的字符转换为数字
    NSString *normalizedText = [self normalizeText:text];
    
    NSMutableString *numbers = [NSMutableString string];
    
    for (NSUInteger i = 0; i < normalizedText.length; i++) {
        unichar character = [normalizedText characterAtIndex:i];
        if (character >= '0' && character <= '9') {
            [numbers appendFormat:@"%C", character];
        }
    }
    
    return numbers;
}

+ (BOOL)isValidMineValue:(NSString *)mineValue forAmount:(CGFloat)amount {
    if (!mineValue || mineValue.length == 0) {
        return NO;
    }
    
    // 单个0是有效的雷值，只有多个0（如00、000）才无效
    if (mineValue.length > 1) {
        BOOL allZeros = YES;
        for (NSUInteger i = 0; i < mineValue.length; i++) {
            unichar character = [mineValue characterAtIndex:i];
            if (character != '0') {
                allZeros = NO;
                break;
            }
        }
        
        // 如果是多位数且全部都是0，则无效
        if (allZeros) {
            return NO;
        }
    }
    
    NSInteger mineValueInt = [mineValue integerValue];
    
    // 移除雷值不能等于金额本身的限制
    // 因为在实际场景中，雷值等于金额是合理的（如金额30，雷值30）
    
    // 雷值应该在合理范围内
    // 使用更灵活的范围检查
    NSInteger maxMineValue;
    if (amount >= 1.0) {
        // 对于1元及以上的金额，大幅提高上限，允许更大的雷值
        maxMineValue = (NSInteger)(amount * 100000);
    } else {
        // 对于小数金额，使用固定的合理上限
        maxMineValue = 99999;
    }
    
    // 移除下限检查，允许任何非零数字作为雷值
    
    return YES;
}

@end

