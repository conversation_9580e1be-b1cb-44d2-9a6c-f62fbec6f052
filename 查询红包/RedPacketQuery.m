//
//  RedPacketQuery.m
//  钉钉单透
//
//  Created by 小七 on 2025/7/6.
//

#import "RedPacketQuery.h"
#import "okhb.h"
#import "sw.h"
#import "LogFloatingView.h"
#import "RedPacketMineDetector.h"
#import <objc/runtime.h>
// 全局打印控制开关
static BOOL enableDebugLog = NO; // 设置为 NO 关闭打印，YES 开启打印

// 自定义日志宏
#define DebugLog(format, ...) do { \
    if (enableDebugLog) { \
        NSLog(format, ##__VA_ARGS__); \
    } \
} while(0)

// 存储所有活跃的定时器，使用queryKey作为标识符
static NSMutableDictionary *activeTimers = nil;
// 存储查询次数，用于统计查询频率
static NSMutableDictionary *queryCounters = nil;
// 存储超时定时器，用于控制查询超时
static NSMutableDictionary *timeoutTimers = nil;
// 存储红包的初始状态，用于判断是否是重新进入app时的已结束红包
static NSMutableDictionary *initialStatus = nil;
// 存储每个红包的抢包结果，key为queryKey，value为抢包结果
static NSMutableDictionary *grabResults = nil;
// 存储每个红包的雷值出现情况，key为queryKey，value为字典{已出现雷值数组, 未出现雷值数组}
static NSMutableDictionary *mineTrackingResults = nil;
@implementation RedPacketQuery

+ (void)initialize {
    if (self == [RedPacketQuery class]) {
        activeTimers = [[NSMutableDictionary alloc] init];
        queryCounters = [[NSMutableDictionary alloc] init];
        timeoutTimers = [[NSMutableDictionary alloc] init];
        initialStatus = [[NSMutableDictionary alloc] init];
        grabResults = [[NSMutableDictionary alloc] init];
        mineTrackingResults = [[NSMutableDictionary alloc] init];
    }
}

+ (void)queryRedPacketWithClusterId:(NSString *)clusterId
                                sid:(NSString *)sid
                             amount:(NSString *)amount
                               size:(NSString *)size
                           congrats:(NSString *)congrats
                              sname:(NSString *)sname
                              title:(NSString *)title {

    // 预先创建queryKey，避免重复创建
    NSString *queryKey = [NSString stringWithFormat:@"%@_%@", clusterId, sid];

    DebugLog(@"🎯 开始查询红包详细信息");
    DebugLog(@"💰 ===== 红包查询参数 =====");
    DebugLog(@"🆔 红包ID: %@", clusterId);
    DebugLog(@"💬 会话ID: %@", sid);
    DebugLog(@"💵 金额: %@元", amount);
    DebugLog(@"🔢 个数: %@个", size);
    DebugLog(@"🎊 祝福语: %@", congrats);
    DebugLog(@"🔑 查询标识: %@", queryKey);
    DebugLog(@"💰 ========================");

    // 检查是否已经有相同的查询在进行
    @synchronized(activeTimers) {
        if (activeTimers[queryKey]) {
            return;
        }
    }

    // 初始化查询计数器
    @synchronized(queryCounters) {
        queryCounters[queryKey] = @(0);
    }

    // 读取超时配置，默认60秒
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *timeoutString = [defaults stringForKey:@"jy"] ?: @"60";
    NSInteger timeoutSeconds = [timeoutString integerValue];
    if (timeoutSeconds <= 0) {
        timeoutSeconds = 60; // 确保超时时间为正数
    }

    DebugLog(@"⏰ 查询超时时间: %ld秒", (long)timeoutSeconds);

    // 立即开始第一次查询
    [self performSingleQueryWithClusterId:clusterId sid:sid queryKey:queryKey groupName:title senderName:sname congrats:congrats];

    // 创建超时定时器
    dispatch_queue_t timeoutQueue = dispatch_queue_create([[NSString stringWithFormat:@"redpacket.timeout.%@", queryKey] UTF8String], DISPATCH_QUEUE_SERIAL);
    dispatch_source_t timeoutTimer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, timeoutQueue);

    // 设置超时定时器：在指定秒数后触发
    dispatch_source_set_timer(timeoutTimer, dispatch_time(DISPATCH_TIME_NOW, timeoutSeconds * NSEC_PER_SEC), DISPATCH_TIME_FOREVER, 100 * NSEC_PER_MSEC);

    // 设置超时处理
    dispatch_source_set_event_handler(timeoutTimer, ^{
        NSString *grabResult = [NSString stringWithFormat:@"查询超时，放弃抢包"];
        NSString *redPacketId = [NSString stringWithFormat:@"%@_%@", clusterId, sid];
        [RedPacketQuery setGrabResult:grabResult forRedPacketId:redPacketId];
        [[LogFloatingView shared] updateGrabResult:grabResult forRedPacketId:redPacketId];
        DebugLog(@"⏰ 查询超时，停止查询: %@", queryKey);
        [self stopTimerForQueryKey:queryKey];
    });

    // 存储超时定时器
    @synchronized(timeoutTimers) {
        timeoutTimers[queryKey] = timeoutTimer;
    }

    // 启动超时定时器
    dispatch_resume(timeoutTimer);

    // 创建GCD定时器，每0.3秒查询一次
    dispatch_queue_t timerQueue = dispatch_queue_create([[NSString stringWithFormat:@"redpacket.query.%@", queryKey] UTF8String], DISPATCH_QUEUE_SERIAL);
    dispatch_source_t timer = dispatch_source_create(DISPATCH_SOURCE_TYPE_TIMER, 0, 0, timerQueue);

    // 设置定时器参数：0.3秒间隔，立即开始
    dispatch_source_set_timer(timer, dispatch_time(DISPATCH_TIME_NOW, 300 * NSEC_PER_MSEC), 300 * NSEC_PER_MSEC, 50 * NSEC_PER_MSEC);

    // 设置定时器事件处理
    dispatch_source_set_event_handler(timer, ^{
        @synchronized(queryCounters) {
            NSNumber *currentCount = queryCounters[queryKey];
            NSInteger count = [currentCount integerValue] + 1;
            queryCounters[queryKey] = @(count);
            DebugLog(@"🔄 第%ld次查询红包: %@", (long)count, queryKey);
        }

        // 执行查询
        [self performSingleQueryWithClusterId:clusterId sid:sid queryKey:queryKey groupName:title senderName:sname congrats:congrats];
    });

    // 存储定时器
    @synchronized(activeTimers) {
        activeTimers[queryKey] = timer;
    }

    // 启动定时器
    dispatch_resume(timer);

}

// 执行单次查询
+ (void)performSingleQueryWithClusterId:(NSString *)clusterId sid:(NSString *)sid queryKey:(NSString *)queryKey groupName:(NSString *)groupName senderName:(NSString *)senderName congrats:(NSString *)congrats{
    // 在每次查询开始时，如果mydAmount有内容且已经使用过，则清空
    // 这样确保每个红包的抢包结果能正确显示

    Class serviceClass = NSClassFromString(@"DTRedEnvelopServiceFactory");
    id service = [serviceClass performSelector:@selector(defaultServiceIMP)];

    void (^successBlock)(id) = ^(id result) {
        // 解析 DTPayRedEnvelopeClusterDetail 对象
        if (result && [result isKindOfClass:NSClassFromString(@"DTPayRedEnvelopeClusterDetail")]) {
            [self parseRedPacketDetail:result sid:sid queryKey:queryKey groupName:groupName senderName:senderName congrats:congrats];
        }

        // 查询成功，不停止定时器，继续查询

        // 这里可以添加后续的红包抢取逻辑
        // 例如调用 okhb 的抢红包方法
    };

    void (^failureBlock)(NSError *) = ^(NSError *error) {
        // 失败时不停止定时器，继续重试
    };

    long long sidValue = [sid longLongValue];
    long long startTime = 0;
    NSInteger sizeValue = 20;
    long long flowId = 0;

    SEL originalSelector = NSSelectorFromString(@"queryRedEnvelopCluster:clusterId:startTime:size:flowId:successBlock:failureBlock:");
    if ([service respondsToSelector:originalSelector]) {
        NSMethodSignature *signature = [service methodSignatureForSelector:originalSelector];
        if (signature) {
            NSInvocation *invocation = [NSInvocation invocationWithMethodSignature:signature];
            [invocation setTarget:service];
            [invocation setSelector:originalSelector];
            [invocation setArgument:&sidValue atIndex:2];
            [invocation setArgument:&clusterId atIndex:3];
            [invocation setArgument:&startTime atIndex:4];
            [invocation setArgument:&sizeValue atIndex:5];
            [invocation setArgument:&flowId atIndex:6];
            [invocation setArgument:&successBlock atIndex:7];
            [invocation setArgument:&failureBlock atIndex:8];
            [invocation invoke];
        }
    }
}

// 手动停止指定查询的定时器（用于外部控制）
+ (void)stopTimerForQueryKey:(NSString *)queryKey {
    @synchronized(activeTimers) {
        dispatch_source_t timer = activeTimers[queryKey];
        if (timer) {
            dispatch_source_cancel(timer);
            [activeTimers removeObjectForKey:queryKey];
        }
    }

    @synchronized(timeoutTimers) {
        dispatch_source_t timeoutTimer = timeoutTimers[queryKey];
        if (timeoutTimer) {
            dispatch_source_cancel(timeoutTimer);
            [timeoutTimers removeObjectForKey:queryKey];
        }
    }

    @synchronized(queryCounters) {
        [queryCounters removeObjectForKey:queryKey];
    }

    @synchronized(initialStatus) {
        [initialStatus removeObjectForKey:queryKey];
    }

    @synchronized(mineTrackingResults) {
        [mineTrackingResults removeObjectForKey:queryKey];
    }
}





// 解析红包详情对象
+ (void)parseRedPacketDetail:(id)detail sid:(NSString *)sid queryKey:(NSString *)queryKey groupName:(NSString *)groupName senderName:(NSString *)senderName congrats:(NSString *)congrats{
    if ([detail respondsToSelector:@selector(redEnvelopCluster)]) {
        id cluster = [detail valueForKey:@"redEnvelopCluster"];
        if (cluster) {
            [self parseRedEnvelopCluster:cluster sid:sid detail:detail queryKey:queryKey groupName:groupName senderName:senderName congrats:congrats];
        }
    }
}



// 解析红包集群信息
+ (void)parseRedEnvelopCluster:(id)cluster sid:(NSString *)sid detail:(id)detail queryKey:(NSString *)queryKey groupName:(NSString *)groupName senderName:(NSString *)senderName congrats:(NSString *)congrats{

    NSString *clusterId = @"";
    NSString *status = @"";
    NSString *totalAmount = @"";
    NSString *totalSize = @"";
    NSInteger receivedCount = 0;

    // 获取基本信息
    if ([cluster respondsToSelector:@selector(clusterId)]) {
        clusterId = [cluster valueForKey:@"clusterId"] ?: @"";
    }

    if ([cluster respondsToSelector:@selector(status)]) {
        status = [[cluster valueForKey:@"status"] stringValue] ?: @"";
    }

    if ([cluster respondsToSelector:@selector(amount)]) {
        totalAmount = [cluster valueForKey:@"amount"] ?: @"";
    }

    if ([cluster respondsToSelector:@selector(size)]) {
        totalSize = [[cluster valueForKey:@"size"] stringValue] ?: @"";
    }


    
    // 计算已领取信息（通过receivers数组）
    if ([cluster respondsToSelector:@selector(receivers)]) {
        id receivers = [cluster valueForKey:@"receivers"];
        if (receivers && [receivers isKindOfClass:[NSArray class]]) {
            NSArray *receiversArray = (NSArray *)receivers;
            receivedCount = receiversArray.count;
        }
    }

    // 实时计算已领取金额和个数（从红包流水中计算）
    NSString *receivedAmount = @"0";
    NSInteger actualReceivedCount = 0;
    if ([detail respondsToSelector:@selector(redEnvelopFlows)]) {
        id flows = [detail valueForKey:@"redEnvelopFlows"];
        if (flows && [flows isKindOfClass:[NSArray class]]) {
            NSArray *flowArray = (NSArray *)flows;
            double totalReceived = 0.0;
            actualReceivedCount = flowArray.count; // 流水数量就是实际已领取个数

            for (id flow in flowArray) {
                if ([flow respondsToSelector:@selector(amount)]) {
                    NSString *amountStr = [flow valueForKey:@"amount"];
                    if (amountStr) {
                        totalReceived += [amountStr doubleValue];
                    }
                }
            }

            if (totalReceived > 0) {
                receivedAmount = [NSString stringWithFormat:@"%.2f", totalReceived];
            }
        }
    }

    // 计算剩余个数
    NSInteger totalCount = [totalSize integerValue];
    NSInteger remainingCount = totalCount - actualReceivedCount;

    //获取雷值
    NSInteger Amoun = [totalAmount integerValue];
    NSString *lz = [RedPacketMineDetector detectMineValueWithAmount:Amoun message:congrats];

    // 收集已领取金额列表记录
    NSMutableArray *receivedAmounts = [[NSMutableArray alloc] init];
    if ([detail respondsToSelector:@selector(redEnvelopFlows)]) {
        id flows = [detail valueForKey:@"redEnvelopFlows"];
        if (flows && [flows isKindOfClass:[NSArray class]]) {
            NSArray *flowArray = (NSArray *)flows;


            // 按流水倒序处理（第一个领取的在前面）
            NSArray *reversedFlowArray = [[flowArray reverseObjectEnumerator] allObjects];
            for (id flow in reversedFlowArray) {
                if ([flow respondsToSelector:@selector(amount)]) {
                    NSString *amount = [flow valueForKey:@"amount"] ?: @"0";
                    [receivedAmounts addObject:[NSString stringWithFormat:@"%@元", amount]];
                }
            }
        }
    }


    // 计算剩余金额
    double totalAmountValue = [totalAmount doubleValue];
    double receivedAmountValue = [receivedAmount doubleValue];
    double remainingAmount = totalAmountValue - receivedAmountValue;




    // 打印构建红包信息字典之前的详细信息
    DebugLog(@"📊 ===== 红包详细信息汇总 =====");
    DebugLog(@"🆔 红包ID: %@", clusterId);
    DebugLog(@"🆔 会话ID: %@", sid);
    DebugLog(@"📊 状态: %@", status);
    DebugLog(@"💰 总金额: %@元", totalAmount);
    DebugLog(@"🔢 总个数: %@个", totalSize);
    DebugLog(@"✅ 已领取个数: %ld个", (long)actualReceivedCount);
    DebugLog(@"💵 已领取金额: %@元", receivedAmount);
    DebugLog(@"⏳ 剩余个数: %ld个", (long)remainingCount);
    DebugLog(@"💰 剩余金额: %.2f元", remainingAmount);
    DebugLog(@"🎯 雷值: %@", lz ?: @"无");
    DebugLog(@"👥 群名: %@", groupName ?: @"未知群组");
    DebugLog(@"👤 发送者: %@", senderName ?: @"未知发送者");
    DebugLog(@"🎊 祝福语: %@", congrats ?: @"");



    NSString *receivedList = [receivedAmounts componentsJoinedByString:@" "];
    DebugLog(@"💸 已领取列表: %@", receivedList.length > 0 ? receivedList : @"无");
    DebugLog(@"📊 ========================");

    // 记录和检查初始状态
    @synchronized(initialStatus) {
        NSNumber *storedInitialStatus = initialStatus[queryKey];
        if (!storedInitialStatus) {
            // 第一次查询，记录初始状态
            initialStatus[queryKey] = @([status integerValue]);
        }
    }

    // 判断是否应该发送到悬浮窗口
    BOOL shouldSendToFloatingView = NO;
    @synchronized(initialStatus) {
        NSNumber *storedInitialStatus = initialStatus[queryKey];
        NSInteger currentStatusValue = [status integerValue];
        NSInteger initialStatusValue = [storedInitialStatus integerValue];

        if (currentStatusValue == 2) {
            // 当前状态可抢取，总是发送
            shouldSendToFloatingView = YES;
        } else if (initialStatusValue == 2) {
            // 初始状态是可抢取，现在变为不可抢取，说明是刚被抢完，发送最后状态
            shouldSendToFloatingView = YES;
        } else {
            // 初始状态就不可抢取，说明是重新进入app看到的已结束红包，不发送
            shouldSendToFloatingView = NO;
        }
    }

    if (shouldSendToFloatingView) {
        // 获取当前红包的抢包结果
        NSString *currentGrabResult = grabResults[queryKey] ?: @"";

        // 构建红包信息字典
        NSDictionary *redPacketInfo = @{
            @"type": @"redpacket",
            @"redPacketId": queryKey,
            @"groupName": groupName ?: @"未知群组",
            @"senderName": senderName ?: @"未知发送者",
            @"totalCount": totalSize,
            @"totalAmount": [NSString stringWithFormat:@"%@元", totalAmount],
            @"congrats": congrats ?: @"",
            @"mineValue": lz ?: @"识别错误",
            @"receivedList": receivedList.length > 0 ? receivedList : @"",
            @"grabResult": currentGrabResult // 当前红包的抢包结果
        };
        // 发送到悬浮日志窗口
        [[LogFloatingView shared] addRedPacketInfo:redPacketInfo];
    }

    // 判断状态，如果不等于2则停止并移除指定定时器
    if ([status integerValue] != 2) {
        // 红包已结束，发送最后一次更新（包含抢包结果）
        NSString *finalGrabResult = grabResults[queryKey] ?: @"";
        if (shouldSendToFloatingView && finalGrabResult.length > 0) {
            // 构建红包信息字典，包含最终的抢包结果
            NSDictionary *redPacketInfo = @{
                @"type": @"redpacket",
                @"redPacketId": queryKey,
                @"groupName": groupName ?: @"未知群组",
                @"senderName": senderName ?: @"未知发送者",
                @"totalCount": totalSize,
                @"totalAmount": [NSString stringWithFormat:@"%@元", totalAmount],
                @"congrats": congrats ?: @"",
                @"mineValue": lz ?: @"识别错误",
                @"receivedList": receivedList.length > 0 ? receivedList : @"",
                @"grabResult": finalGrabResult // 最终抢包结果
            };
            [[LogFloatingView shared] addRedPacketInfo:redPacketInfo];
        }
        [grabResults removeObjectForKey:queryKey];
        [self stopTimerForQueryKey:queryKey];
        return;
    }

    
    
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    bool  wb =  [defaults boolForKey:@"wb"];
    bool  zjq =  [defaults boolForKey:@"zjq"];
    NSString *dlxConfig = [defaults objectForKey:@"dlx"]; // 雷类型配置

    
    if(wb == YES){
        if(remainingCount == 1){
            if([dlxConfig isEqualToString:@"0"]){
                if(lz.length == 1){
                    //单雷+扫尾
                    bool dl = [self shouldGrabWithSingleMineAndSweep:remainingCount remainingAmount:remainingAmount mineValue:lz];
                    if(dl == YES){
                        [self sid:sid clusterId:clusterId];
                        return;
                    }else{
                        NSString *grabResult = [NSString stringWithFormat:@"尾包是雷，放弃抢包"];
                        NSString *redPacketId = [NSString stringWithFormat:@"%@_%@", clusterId, sid];
                        [RedPacketQuery setGrabResult:grabResult forRedPacketId:redPacketId];
                        [[LogFloatingView shared] updateGrabResult:grabResult forRedPacketId:redPacketId];
                        [self stopTimerForQueryKey:queryKey];
                        return;
                        
                    }
                }else{
                    NSString *grabResult = [NSString stringWithFormat:@"不是单雷，放弃抢包"];
                    NSString *redPacketId = [NSString stringWithFormat:@"%@_%@", clusterId, sid];
                    [RedPacketQuery setGrabResult:grabResult forRedPacketId:redPacketId];
                    [[LogFloatingView shared] updateGrabResult:grabResult forRedPacketId:redPacketId];
                    [self stopTimerForQueryKey:queryKey];
                    return;
                }
            }else if ([dlxConfig isEqualToString:@"1"]){
                if(lz.length > 1){
                    //多雷+扫尾
                    bool dl = [self shouldGrabWithMultiMineAndSweep:remainingCount remainingAmount:remainingAmount mineValue:lz];
                    if(dl == YES){
                        [self sid:sid clusterId:clusterId];
                        return;
                    }else{
                        NSString *grabResult = [NSString stringWithFormat:@"尾包是雷，放弃抢包"];
                        NSString *redPacketId = [NSString stringWithFormat:@"%@_%@", clusterId, sid];
                        [RedPacketQuery setGrabResult:grabResult forRedPacketId:redPacketId];
                        [[LogFloatingView shared] updateGrabResult:grabResult forRedPacketId:redPacketId];
                        [self stopTimerForQueryKey:queryKey];
                        return;
                    }
                }else{
                    NSString *grabResult = [NSString stringWithFormat:@"不是多雷，放弃抢包"];
                    NSString *redPacketId = [NSString stringWithFormat:@"%@_%@", clusterId, sid];
                    [RedPacketQuery setGrabResult:grabResult forRedPacketId:redPacketId];
                    [[LogFloatingView shared] updateGrabResult:grabResult forRedPacketId:redPacketId];
                    [self stopTimerForQueryKey:queryKey];
                    return;
                }
            }else if ([dlxConfig isEqualToString:@"2"]){
                if(lz.length >= 1){
                    //智能雷+扫尾
                    bool dl = [self shouldGrabWithMultiMineAndSweep:remainingCount remainingAmount:remainingAmount mineValue:lz];
                    if(dl == YES){
                        [self sid:sid clusterId:clusterId];
                        return;
                    }else{
                        NSString *grabResult = [NSString stringWithFormat:@"尾包是雷，放弃抢包"];
                        NSString *redPacketId = [NSString stringWithFormat:@"%@_%@", clusterId, sid];
                        [RedPacketQuery setGrabResult:grabResult forRedPacketId:redPacketId];
                        [[LogFloatingView shared] updateGrabResult:grabResult forRedPacketId:redPacketId];
                        [self stopTimerForQueryKey:queryKey];
                        return;
                    }
                }
            }
        }
        
    }else if(zjq == YES){
        //中间抢+扫尾
            int dl = [self shouldGrabWithMiddleAndSweep:remainingCount remainingAmount:remainingAmount mineValue:lz totalAmount:totalAmount totalCount:totalCount queryKey:queryKey detail:detail];
            if(dl == 0){
                [self sid:sid clusterId:clusterId];
                return;
            }else if(dl == 1){
                NSString *grabResult = [NSString stringWithFormat:@"会中雷，放弃抢包"];
                NSString *redPacketId = [NSString stringWithFormat:@"%@_%@", clusterId, sid];
                [RedPacketQuery setGrabResult:grabResult forRedPacketId:redPacketId];
                [[LogFloatingView shared] updateGrabResult:grabResult forRedPacketId:redPacketId];
                [self stopTimerForQueryKey:queryKey];
            }
    }else{
        [self stopTimerForQueryKey:queryKey];
        return;
    }
}
//抢红包
+(void)sid:(NSString *)sid clusterId:(NSString *)clusterId{
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString * ycc =  [defaults stringForKey:@"yc"]?: @"0";
    const char *cStr = [ycc UTF8String];
    char *endPtr;
    unsigned long long ycvalue = strtoull(cStr, &endPtr, 10);
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(ycvalue * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        dispatch_async(dispatch_get_main_queue(), ^{
            [okhb sid:sid clusterId:clusterId zfy:@"1"];
        });
    });
    return;
    
    
}

// 设置指定红包的抢包结果
+ (void)setGrabResult:(NSString *)grabResult forRedPacketId:(NSString *)redPacketId {
    if (!grabResult || !redPacketId) return;
    @synchronized(grabResults) {
        grabResults[redPacketId] = grabResult;
    }
}

// 单雷+扫尾判断方法
+ (BOOL)shouldGrabWithSingleMineAndSweep:(NSInteger)remainingCount remainingAmount:(double)remainingAmount mineValue:(NSString *)mineValue {
    // 读取配置
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *lwzConfig = [defaults objectForKey:@"lwz"]; // 雷位配置
    NSString *dlxConfig = [defaults objectForKey:@"dlx"]; // 雷类型配置

    DebugLog(@"🎯 单雷+扫尾判断开始");
    DebugLog(@"📊 剩余个数: %ld", (long)remainingCount);
    DebugLog(@"💰 剩余金额: %.2f", remainingAmount);
    DebugLog(@"⚡ 雷值: %@", mineValue ?: @"无");
    DebugLog(@"⚙️ 雷位配置: %@", lwzConfig ?: @"未配置");
    DebugLog(@"⚙️ 雷类型配置: %@", dlxConfig ?: @"未配置");


    // 根据雷类型配置获取剩余金额的尾数
    NSString *lastDigit = @"";
    if ([lwzConfig isEqualToString:@"0"]) {
        // 分为雷
        NSString *remainAmountStr = [NSString stringWithFormat:@"%.2f", remainingAmount];
        lastDigit = [remainAmountStr substringFromIndex:remainAmountStr.length - 1];
        DebugLog(@"🔢 分为雷模式，剩余金额: %@，尾数: %@", remainAmountStr, lastDigit);
    } else if ([lwzConfig isEqualToString:@"1"]) {
        // 角为雷
        NSString *remainAmountStr = [NSString stringWithFormat:@"%.1f", remainingAmount];
        lastDigit = [remainAmountStr substringFromIndex:remainAmountStr.length - 1];
        DebugLog(@"🔢 角为雷模式，剩余金额: %@，尾数: %@", remainAmountStr, lastDigit);
    } else if ([lwzConfig isEqualToString:@"2"]) {
        // 元为雷
        NSString *remainAmountStr = [NSString stringWithFormat:@"%.0f", remainingAmount];
        lastDigit = [remainAmountStr substringFromIndex:remainAmountStr.length - 1];
        DebugLog(@"🔢 元为雷模式，剩余金额: %@，尾数: %@", remainAmountStr, lastDigit);
    }

    // 判断尾数是否等于雷值
    if ([lastDigit isEqualToString:mineValue]) {
        DebugLog(@"💣 尾数 %@ 等于雷值 %@，不抢包", lastDigit, mineValue);
        return NO;
    } else {
        DebugLog(@"✅ 尾数 %@ 不等于雷值 %@，可以抢包", lastDigit, mineValue);
        return YES;
    }
}

// 多雷+扫尾判断方法
+ (BOOL)shouldGrabWithMultiMineAndSweep:(NSInteger)remainingCount remainingAmount:(double)remainingAmount mineValue:(NSString *)mineValue {
    // 读取配置
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *dlxConfig = [defaults objectForKey:@"dlx"]; // 雷类型配置
    NSString *lwzConfig = [defaults objectForKey:@"lwz"]; // 雷位配置
    DebugLog(@"🎯 多雷+扫尾判断开始");
    DebugLog(@"📊 剩余个数: %ld", (long)remainingCount);
    DebugLog(@"💰 剩余金额: %.2f", remainingAmount);
    DebugLog(@"⚡ 雷值: %@", mineValue ?: @"无");
    DebugLog(@"⚙️ 雷类型配置: %@", dlxConfig ?: @"未配置");
    DebugLog(@"🔢 雷值长度: %ld", (long)mineValue.length);


    // 根据雷类型配置获取剩余金额的尾数
    NSString *lastDigit = @"";
    if ([lwzConfig isEqualToString:@"0"]) {
        // 分为雷
        NSString *remainAmountStr = [NSString stringWithFormat:@"%.2f", remainingAmount];
        lastDigit = [remainAmountStr substringFromIndex:remainAmountStr.length - 1];
        DebugLog(@"🔢 分为雷模式，剩余金额: %@，尾数: %@", remainAmountStr, lastDigit);
    } else if ([lwzConfig isEqualToString:@"1"]) {
        // 角为雷
        NSString *remainAmountStr = [NSString stringWithFormat:@"%.1f", remainingAmount];
        lastDigit = [remainAmountStr substringFromIndex:remainAmountStr.length - 1];
        DebugLog(@"🔢 角为雷模式，剩余金额: %@，尾数: %@", remainAmountStr, lastDigit);
    } else if ([lwzConfig isEqualToString:@"2"]) {
        // 元为雷
        NSString *remainAmountStr = [NSString stringWithFormat:@"%.0f", remainingAmount];
        lastDigit = [remainAmountStr substringFromIndex:remainAmountStr.length - 1];
        DebugLog(@"🔢 元为雷模式，剩余金额: %@，尾数: %@", remainAmountStr, lastDigit);
    }

    // 判断尾数是否包含在雷值中
    if ([mineValue containsString:lastDigit]) {
        DebugLog(@"💣 尾数 %@ 包含在雷值 %@ 中，不抢包", lastDigit, mineValue);
        return NO;
    } else {
        DebugLog(@"✅ 尾数 %@ 不包含在雷值 %@ 中，可以抢包", lastDigit, mineValue);
        return YES;
    }
}

// 中间抢+扫尾判断方法
+ (int)shouldGrabWithMiddleAndSweep:(NSInteger)remainingCount remainingAmount:(double)remainingAmount mineValue:(NSString *)mineValue totalAmount:(NSString *)totalAmount totalCount:(NSInteger)totalCount queryKey:(NSString *)queryKey detail:(id)detail {

    DebugLog(@"🎯 中间抢+扫尾判断开始");
    DebugLog(@"📊 剩余个数: %ld", (long)remainingCount);
    DebugLog(@"💰 剩余金额: %.2f", remainingAmount);
    DebugLog(@"⚡ 雷值: %@", mineValue ?: @"无");
    DebugLog(@"💵 总金额: %@", totalAmount);
    DebugLog(@"🔢 总个数: %ld", (long)totalCount);


    // 更新雷值跟踪情况
    [self updateMineTrackingForQueryKey:queryKey mineValue:mineValue detail:detail];

    // 获取当前雷值跟踪情况
    NSDictionary *mineTracking = [self getMineTrackingForQueryKey:queryKey];
    NSArray *appearedMines = mineTracking[@"appeared"] ?: @[];
    NSArray *notAppearedMines = mineTracking[@"notAppeared"] ?: @[];

    DebugLog(@"📈 已出现雷值: %@", appearedMines);
    DebugLog(@"📉 未出现雷值: %@", notAppearedMines);
    
    //没人领取继续等待
    if(totalCount == remainingCount){
        return 2;
    }

    // 规则1: 如果未出现雷值数量大于剩余包数，直接抢
    if (notAppearedMines.count > remainingCount) {
        DebugLog(@"✅ 未出现雷值数量(%ld) > 剩余包数(%ld)，直接抢包", (long)notAppearedMines.count, (long)remainingCount);
        return 0;
    }

    // 规则2: 如果剩余包数 == 1，进入扫尾判断
    if (remainingCount == 1) {
        return [self shouldGrabLastPacketWithMineTracking:mineTracking remainingAmount:remainingAmount mineValue:mineValue];
    }

    // 规则3: 中间包剩余金额判断
    // 保守策略：只有百分百确定安全才抢包，否则等待下一个人抢包后重新计算
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *lwzConfig = [defaults objectForKey:@"lwz"] ?: @"0";

    DebugLog(@"💰 剩余金额分析: 总额%.2f, 剩余%ld个", remainingAmount, (long)remainingCount);
    DebugLog(@"📉 未出现雷值: %@", notAppearedMines);


    // 百分百安全检查：分析剩余金额是否在任何分配情况下都不可能形成全雷
    BOOL absolutelySafe = [self isAbsolutelySafeToGrab:remainingAmount
                                        remainingCount:remainingCount
                                      notAppearedMines:notAppearedMines
                                             lwzConfig:lwzConfig];

    if (absolutelySafe) {
        DebugLog(@"✅ 百分百确定安全，可以抢包");
        return 0;
    } else {
        DebugLog(@"⚠️ 无法百分百确定安全，继续等待下一个人抢包");
        return 2;
    }
}

// 更新雷值跟踪情况
+ (void)updateMineTrackingForQueryKey:(NSString *)queryKey mineValue:(NSString *)mineValue detail:(id)detail {
    @synchronized(mineTrackingResults) {
        NSMutableDictionary *tracking = mineTrackingResults[queryKey];
        if (!tracking) {
            // 初始化雷值跟踪
            tracking = [[NSMutableDictionary alloc] init];
            NSMutableArray *appeared = [[NSMutableArray alloc] init];
            NSMutableArray *notAppeared = [[NSMutableArray alloc] init];

            // 将雷值的每个字符作为独立的雷值
            for (int i = 0; i < mineValue.length; i++) {
                NSString *singleMine = [mineValue substringWithRange:NSMakeRange(i, 1)];
                [notAppeared addObject:singleMine];
            }

            tracking[@"appeared"] = appeared;
            tracking[@"notAppeared"] = notAppeared;
            mineTrackingResults[queryKey] = tracking;
        }

        // 检查已领取的红包，更新雷值状态
        if ([detail respondsToSelector:@selector(redEnvelopFlows)]) {
            id flows = [detail valueForKey:@"redEnvelopFlows"];
            if (flows && [flows isKindOfClass:[NSArray class]]) {
                NSArray *flowArray = (NSArray *)flows;
                NSMutableArray *appeared = [tracking[@"appeared"] mutableCopy];
                NSMutableArray *notAppeared = [tracking[@"notAppeared"] mutableCopy];

                for (id flow in flowArray) {
                    if ([flow respondsToSelector:@selector(amount)]) {
                        NSString *amountStr = [flow valueForKey:@"amount"];
                        if (amountStr) {
                            // 获取金额的尾数
                            NSString *lastDigit = [amountStr substringFromIndex:amountStr.length - 1];

                            // 如果这个尾数在未出现列表中，移动到已出现列表
                            if ([notAppeared containsObject:lastDigit] && ![appeared containsObject:lastDigit]) {
                                [appeared addObject:lastDigit];
                                [notAppeared removeObject:lastDigit];
                            }
                        }
                    }
                }

                tracking[@"appeared"] = appeared;
                tracking[@"notAppeared"] = notAppeared;
            }
        }
    }
}

// 获取雷值跟踪情况
+ (NSDictionary *)getMineTrackingForQueryKey:(NSString *)queryKey {
    @synchronized(mineTrackingResults) {
        return mineTrackingResults[queryKey] ?: @{@"appeared": @[], @"notAppeared": @[]};
    }
}

// 扫尾判断 - 剩余1个包时的逻辑
+ (int)shouldGrabLastPacketWithMineTracking:(NSDictionary *)mineTracking remainingAmount:(double)remainingAmount mineValue:(NSString *)mineValue {
    NSArray *appearedMines = mineTracking[@"appeared"] ?: @[];
    NSArray *notAppearedMines = mineTracking[@"notAppeared"] ?: @[];

    DebugLog(@"🎯 扫尾判断开始");
    DebugLog(@"📈 已出现雷值: %@", appearedMines);
    DebugLog(@"📉 未出现雷值: %@", notAppearedMines);

    // 读取雷类型配置
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    NSString *lwzConfig = [defaults objectForKey:@"lwz"]; // 雷位配置

    // 根据雷类型配置获取剩余金额的尾数
    NSString *lastDigit = @"";
    if ([lwzConfig isEqualToString:@"0"]) {
        // 分为雷
        NSString *remainAmountStr = [NSString stringWithFormat:@"%.2f", remainingAmount];
        lastDigit = [remainAmountStr substringFromIndex:remainAmountStr.length - 1];
        DebugLog(@"🔢 分为雷模式，剩余金额: %@，尾数: %@", remainAmountStr, lastDigit);
    } else if ([lwzConfig isEqualToString:@"1"]) {
        // 角为雷
        NSString *remainAmountStr = [NSString stringWithFormat:@"%.1f", remainingAmount];
        lastDigit = [remainAmountStr substringFromIndex:remainAmountStr.length - 1];
        DebugLog(@"🔢 角为雷模式，剩余金额: %@，尾数: %@", remainAmountStr, lastDigit);
    } else if ([lwzConfig isEqualToString:@"2"]) {
        // 元为雷
        NSString *remainAmountStr = [NSString stringWithFormat:@"%.0f", remainingAmount];
        lastDigit = [remainAmountStr substringFromIndex:remainAmountStr.length - 1];
        DebugLog(@"🔢 元为雷模式，剩余金额: %@，尾数: %@", remainAmountStr, lastDigit);
    }

    // 判断尾数会不会导致重复雷值或全雷
    if ([appearedMines containsObject:lastDigit]) {
        DebugLog(@"💣 尾数 %@ 已经出现过，会导致重复雷值，不抢包", lastDigit);
        return 1;
    }

    if ([notAppearedMines containsObject:lastDigit] && notAppearedMines.count == 1) {
        DebugLog(@"💣 尾数 %@ 会导致全雷出现，不抢包", lastDigit);
        return 1;
    }

    // 其他情况可以抢
    DebugLog(@"✅ 尾数 %@ 不会导致重复雷值或全雷，可以抢包", lastDigit);
    return 0;
}

// 百分百安全检查：分析剩余金额在任何分配情况下都不可能形成全雷
+ (BOOL)isAbsolutelySafeToGrab:(double)remainingAmount
                remainingCount:(NSInteger)remainingCount
              notAppearedMines:(NSArray *)notAppearedMines
                     lwzConfig:(NSString *)lwzConfig {

    DebugLog(@"🔍 开始百分百安全检查");

    // 基础检查：如果剩余包数小于未出现雷值数，不可能全雷
    if (remainingCount < notAppearedMines.count) {
        DebugLog(@"✅ 剩余包数(%ld) < 未出现雷值数(%ld)，百分百安全",
                (long)remainingCount, (long)notAppearedMines.count);
        return YES;
    }

    // 金额约束检查：计算每个雷值的最小金额需求
    double totalMinAmountForAllMines = 0.0;
    for (NSString *mine in notAppearedMines) {
        double minAmount = [self getMinAmountForDigit:mine lwzConfig:lwzConfig];
        totalMinAmountForAllMines += minAmount;
    }

    // 剩余包数减去雷值包数后，其他包的最小金额
    NSInteger nonMinePackets = remainingCount - notAppearedMines.count;
    double minAmountForNonMinePackets = nonMinePackets * 0.01;

    double totalMinAmountNeeded = totalMinAmountForAllMines + minAmountForNonMinePackets;

    DebugLog(@"💰 形成全雷最小金额需求: %.2f (雷值包%.2f + 其他包%.2f)",
            totalMinAmountNeeded, totalMinAmountForAllMines, minAmountForNonMinePackets);

    if (remainingAmount < totalMinAmountNeeded) {
        DebugLog(@"✅ 剩余金额(%.2f) < 全雷最小需求(%.2f)，百分百安全",
                remainingAmount, totalMinAmountNeeded);
        return YES;
    }

    // 更严格的检查：考虑红包分配的上限约束
    // 每个红包的最大金额不能超过 剩余金额 - (剩余包数-1) * 0.01
    double maxSingleAmount = remainingAmount - (remainingCount - 1) * 0.01;

    // 检查是否有雷值无法在最大单包金额下产生
    for (NSString *mine in notAppearedMines) {
        if (![self canAmountProduceDigit:mine maxAmount:maxSingleAmount lwzConfig:lwzConfig]) {
            DebugLog(@"✅ 雷值%@无法在最大单包金额%.2f下产生，百分百安全", mine, maxSingleAmount);
            return YES;
        }
    }

    DebugLog(@"⚠️ 无法百分百确定安全，存在形成全雷的可能性");
    return NO;
}



// 检查指定最大金额是否能产生指定尾数
+ (BOOL)canAmountProduceDigit:(NSString *)digit
                    maxAmount:(double)maxAmount
                    lwzConfig:(NSString *)lwzConfig {

    NSInteger digitValue = [digit integerValue];

    if ([lwzConfig isEqualToString:@"0"]) {
        // 分为雷：检查0.01到maxAmount范围内是否有尾数为digit的金额
        for (int cent = 1; cent <= (int)(maxAmount * 100); cent++) {
            if (cent % 10 == digitValue) {
                return YES;
            }
        }
    } else if ([lwzConfig isEqualToString:@"1"]) {
        // 角为雷：检查0.1到maxAmount范围内是否有尾数为digit的金额
        for (int jiao = 1; jiao <= (int)(maxAmount * 10); jiao++) {
            if (jiao % 10 == digitValue) {
                return YES;
            }
        }
    } else {
        // 元为雷：检查1到maxAmount范围内是否有尾数为digit的金额
        for (int yuan = 1; yuan <= (int)maxAmount; yuan++) {
            if (yuan % 10 == digitValue) {
                return YES;
            }
        }
    }

    return NO;
}



// 获取产生指定尾数的最小金额
+ (double)getMinAmountForDigit:(NSString *)digit lwzConfig:(NSString *)lwzConfig {
    NSInteger digitValue = [digit integerValue];

    if ([lwzConfig isEqualToString:@"0"]) {
        // 分为雷：0.0X
        return digitValue == 0 ? 0.10 : (digitValue * 0.01);
    } else if ([lwzConfig isEqualToString:@"1"]) {
        // 角为雷：0.X
        return digitValue == 0 ? 1.0 : (digitValue * 0.1);
    } else {
        // 元为雷：X
        return digitValue == 0 ? 10.0 : digitValue;
    }
}



@end
