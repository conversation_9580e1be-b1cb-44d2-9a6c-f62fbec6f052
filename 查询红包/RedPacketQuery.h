//
//  RedPacketQuery.h
//  钉钉单透
//
//  Created by 小七 on 2025/7/6.
//

#import <Foundation/Foundation.h>

@interface RedPacketQuery : NSObject


+ (void)queryRedPacketWithClusterId:(NSString *)clusterId
                                sid:(NSString *)sid
                             amount:(NSString *)amount
                               size:(NSString *)size
                           congrats:(NSString *)congrats
                              sname:(NSString *)sname
                              title:(NSString *)title;

// 定时器管理方法
+ (void)stopTimerForQueryKey:(NSString *)queryKey;

// 抢包结果管理方法
+ (void)setGrabResult:(NSString *)grabResult forRedPacketId:(NSString *)redPacketId;

// 单雷+扫尾判断方法
+ (BOOL)shouldGrabWithSingleMineAndSweep:(NSInteger)remainingCount remainingAmount:(double)remainingAmount mineValue:(NSString *)mineValue;

// 多雷+扫尾判断方法
+ (BOOL)shouldGrabWithMultiMineAndSweep:(NSInteger)remainingCount remainingAmount:(double)remainingAmount mineValue:(NSString *)mineValue;

// 中间抢+扫尾判断方法
+ (int)shouldGrabWithMiddleAndSweep:(NSInteger)remainingCount remainingAmount:(double)remainingAmount mineValue:(NSString *)mineValue totalAmount:(NSString *)totalAmount totalCount:(NSInteger)totalCount queryKey:(NSString *)queryKey detail:(id)detail;

// 雷值跟踪辅助方法
+ (void)updateMineTrackingForQueryKey:(NSString *)queryKey mineValue:(NSString *)mineValue detail:(id)detail;
+ (NSDictionary *)getMineTrackingForQueryKey:(NSString *)queryKey;
+ (int)shouldGrabLastPacketWithMineTracking:(NSDictionary *)mineTracking remainingAmount:(double)remainingAmount mineValue:(NSString *)mineValue;
+ (int)shouldGrabMiddlePacketWithMineTracking:(NSDictionary *)mineTracking remainingCount:(NSInteger)remainingCount remainingAmount:(double)remainingAmount mineValue:(NSString *)mineValue;
+ (int)canAvoidFullMineWithRemainingAmount:(double)remainingAmount remainingCount:(NSInteger)remainingCount notAppearedMines:(NSArray *)notAppearedMines;

@end
