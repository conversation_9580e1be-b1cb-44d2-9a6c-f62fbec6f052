//
//  okhb.m
//  钉钉单透
//
//  Created by 小七 on 2025/7/6.
//

#import "okhb.h"
#import "sw.h"
#import <objc/message.h>
#import <objc/runtime.h>
#import <AVFoundation/AVFoundation.h>
#import <UIKit/UIKit.h>
#import "LogFloatingView.h"
#import "RedPacketQuery.h"
@interface okhb () <AVSpeechSynthesizerDelegate>
@property (nonatomic, strong) AVSpeechSynthesizer *synthesizer;
@property (nonatomic, strong) AVAudioSession *audioSession;
@end

@implementation okhb

static NSMutableSet *grabbedRedPackets = nil;

+ (void)sid:(NSString *)sid clusterId:(NSString *)clusterId zfy:(NSString *)zfy{
    if (!grabbedRedPackets) grabbedRedPackets = [[NSMutableSet alloc] init];

    NSString *redPacketKey = clusterId;
    if ([grabbedRedPackets containsObject:redPacketKey]) {
        return;
    }
    [grabbedRedPackets addObject:redPacketKey];
    long long sidValue = [sid longLongValue];
    Class factoryClass = objc_getClass("DTRedEnvelopServiceFactory");
    SEL defaultServiceSelector = @selector(defaultServiceIMP);
    id imp = nil;
    
    if ([factoryClass respondsToSelector:defaultServiceSelector]) {
        imp = ((id(*)(Class, SEL))objc_msgSend)(factoryClass, defaultServiceSelector);
    }
    
    // 创建成功回调来获取抢到的金额
    void (^successBlock)(id) = ^(id result) {
        id pickStatus = [result valueForKey:@"pickStatus"];
        BOOL isSuccess = [pickStatus intValue] == 1;
        
        if (isSuccess) {
            // 获取金额
            NSString *amount = @"0";
            id redEnvelopClusterDetail = [result valueForKey:@"redEnvelopClusterDetail"];
            if (redEnvelopClusterDetail) {
                id pickedFlow = [redEnvelopClusterDetail valueForKey:@"pickedFlow"];
                if (pickedFlow) {
                    id amountValue = [pickedFlow valueForKey:@"amount"];
                    if (amountValue) {
                        amount = [amountValue stringValue];
                    }
                }
            }
              // NSLog(@"✅ 抢包成功 | 金额: %@元", amount);
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
                bool  yy =  [defaults boolForKey:@"yy"];
            if(yy == YES){
                [self speakAmount:[NSString stringWithFormat:@"抢到红包%@元", amount]];
            }
            dispatch_async(dispatch_get_main_queue(), ^{
                NSString *grabResult = [NSString stringWithFormat:@"恭喜，抢到红包 %@ 元", amount];
                NSString *redPacketId = [NSString stringWithFormat:@"%@_%@", clusterId, sid];

                // 存储到对应红包的抢包结果中
                [RedPacketQuery setGrabResult:grabResult forRedPacketId:redPacketId];

                [[LogFloatingView shared] updateGrabResult:grabResult forRedPacketId:redPacketId];
            });
            
        } else {
            dispatch_async(dispatch_get_main_queue(), ^{
                NSString *grabResult = @"手慢了，没抢到";
                NSString *redPacketId = [NSString stringWithFormat:@"%@_%@", clusterId, sid];
                // 存储到对应红包的抢包结果中
                [RedPacketQuery setGrabResult:grabResult forRedPacketId:redPacketId];

                [[LogFloatingView shared] updateGrabResult:grabResult forRedPacketId:redPacketId];
            });
//            NSLog(@"❌ 抢包失败");
        }
        
    };
    
    // 使用objc_msgSend调用V2版本方法
    SEL v2Selector = @selector(pickRedEnvelopClusterV2:clusterId:successBlock:failureBlock:);
    SEL v1Selector = @selector(pickRedEnvelopCluster:clusterId:successBlock:failureBlock:);
    
    if ([imp respondsToSelector:v2Selector]) {
  
        ((void(*)(id, SEL, long long, NSString*, id, id))objc_msgSend)(imp, v2Selector, sidValue, clusterId, successBlock, nil);
   
    } else if ([imp respondsToSelector:v1Selector]) {

        ((void(*)(id, SEL, long long, NSString*, id, id))objc_msgSend)(imp, v1Selector, sidValue, clusterId, successBlock, nil);
  
    }
    
}
+ (void)speakAmount:(NSString *)amount {
    //dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
    dispatch_async(dispatch_get_main_queue(), ^{
        AVSpeechUtterance *utterance = [AVSpeechUtterance speechUtteranceWithString:amount];
        AVSpeechSynthesisVoice *voice = [AVSpeechSynthesisVoice voiceWithIdentifier:@"com.apple.ttsbundle.siri_Tingting_zh-CN_compact"];
        utterance.voice = voice;
        utterance.rate = 0.7;
        utterance.volume = 1.0;
        utterance.pitchMultiplier = 1.0;
        
        AVSpeechSynthesizer *synthesizer = [[AVSpeechSynthesizer alloc] init];
        [synthesizer speakUtterance:utterance];
    });
}

@end
