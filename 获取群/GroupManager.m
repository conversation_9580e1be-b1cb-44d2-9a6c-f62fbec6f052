//
//  GroupManager.m
//  获取群功能
//
//  Created by Assistant
//

#import "GroupManager.h"
#import "MBProgressHUD.h"
#import "CaptainHook.h"
#import <objc/runtime.h>
#import <UIKit/UIKit.h>
static NSString *qm;
static NSString *userName;
@implementation GroupManager
CHDeclareClass(DTConversationSettingsViewController);

CHOptimizedMethod0(self, void, DTConversationSettingsViewController, viewDidLoad) {

    CHSuper0(DTConversationSettingsViewController, viewDidLoad);

    UIView *mainView = ((UIView *(*)(id, SEL))objc_msgSend)(self, @selector(view));
    UIButton *customButton = [UIButton buttonWithType:UIButtonTypeSystem];
    customButton.frame = CGRectMake(240, 80, 80, 40);
    [customButton setTitle:@"指定群" forState:UIControlStateNormal];
    [customButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    customButton.backgroundColor = [UIColor systemBlueColor];
    customButton.layer.cornerRadius = 8;


    UIAction *action = [UIAction actionWithTitle:@"" image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {

        if (qm) {
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            NSString * q = [defaults stringForKey:@"zdq"];
            if([q isEqualToString:qm]){
                [defaults removeObjectForKey:@"zdq"];
                [defaults synchronize];
                [GroupManager T:@"移除指定群成功"];
                
            }else{
                [defaults setObject:qm forKey:@"zdq"];
                [defaults setBool:NO forKey:@"qbq"];
                [defaults synchronize];
                [GroupManager T:@"添加指定群成功"];
                
            }
        }
    }];
    [customButton addAction:action forControlEvents:UIControlEventTouchUpInside];
    [mainView addSubview:customButton];
}


CHOptimizedMethod0(self, id, DTConversationSettingsViewController, conversation) {
    id ret = CHSuper0(DTConversationSettingsViewController, conversation);
        if ([ret respondsToSelector:@selector(title)]) {
            id title = ((id (*)(id, SEL))objc_msgSend)(ret, @selector(title));
            qm = title;
        }
    return ret;
}

CHDeclareClass(ADTUserProfileViewController);

CHOptimizedMethod0(self, void, ADTUserProfileViewController, viewDidLoad) {

    CHSuper0(ADTUserProfileViewController, viewDidLoad);

    UIView *mainView = ((UIView *(*)(id, SEL))objc_msgSend)(self, @selector(view));
    UIButton *customButton = [UIButton buttonWithType:UIButtonTypeSystem];
    customButton.frame = CGRectMake(240, 180, 80, 40);
    [customButton setTitle:@"指定人" forState:UIControlStateNormal];
    [customButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    customButton.backgroundColor = [UIColor systemBlueColor];
    customButton.layer.cornerRadius = 8;

    // 使用 block 方式处理按钮点击，避免方法不存在的崩溃
    UIAction *action = [UIAction actionWithTitle:@"" image:nil identifier:nil handler:^(__kindof UIAction * _Nonnull action) {

        if (userName) {
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            NSString * r = [defaults stringForKey:@"zdr"];
            if([r isEqualToString:userName]){
                [defaults removeObjectForKey:@"zdr"];
                [defaults synchronize];
                [GroupManager T:@"移除指定人成功"];
                
            }else{
                [defaults setObject:userName forKey:@"zdr"];
                [defaults synchronize];
                [GroupManager T:@"添加指定人成功"];
                
            }

        }
    }];
    [customButton addAction:action forControlEvents:UIControlEventTouchUpInside];
    [mainView addSubview:customButton];
}



CHOptimizedMethod0(self, id, ADTUserProfileViewController, viewModel) {
    id ret = CHSuper0(ADTUserProfileViewController, viewModel);
        if ([ret respondsToSelector:@selector(userContact)]) {
            id userContact = ((id (*)(id, SEL))objc_msgSend)(ret, @selector(userContact));
            if ([userContact respondsToSelector:@selector(displayName)]) {
                id displayName = ((id (*)(id, SEL))objc_msgSend)(userContact, @selector(displayName));
                userName = displayName;
            }
        }

    return ret;
}
+(void)T:(NSString *)str{
    MBProgressHUD *progress = [MBProgressHUD showHUDAddedTo:[UIApplication sharedApplication].keyWindow animated:YES];
    progress.mode = MBProgressHUDModeText;
    progress.label.text = str;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        [MBProgressHUD hideHUDForView:[UIApplication sharedApplication].keyWindow animated:YES];
    });
    
}
CHConstructor{
    CHLoadLateClass(DTConversationSettingsViewController);
    CHHook0(DTConversationSettingsViewController, viewDidLoad);
    CHHook0(DTConversationSettingsViewController, conversation);
    CHLoadLateClass(ADTUserProfileViewController);
    CHHook0(ADTUserProfileViewController, viewDidLoad);
    CHHook0(ADTUserProfileViewController, viewModel);

}
@end
