//
//  sw.m
//  钉钉单透
//
//  Created by 小七 on 2025/7/8.
//

#import "sw.h"
#import "MenuViewController.h"
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import <openssl/rsa.h>
#import <openssl/pem.h>
#import <string.h>
#import <stdlib.h>
#import "curl.h"
#include <stdio.h>
#include <time.h>
#include <stdint.h>
#import <objc/runtime.h>
#include <objc/message.h>
#include <stdbool.h>
#include <ctype.h>
#include <limits.h>
#include <sys/mount.h>
#import <openssl/md5.h>
bool swww = NO;
NSString * dq = @"未激活";

// RSA公钥
static const char *PUBLIC_KEY = "-----BEGIN PUBLIC KEY-----\n"\
"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAy2AgApjTeCAu22j26d0W\n"\
"j1yr4B39xJMWOyZTlodE/jWR4G1UHgzBRjkhkP4qQJjKlwra8RFRjQhPjfinQIRE\n"\
"NszS70WfYiucvZuL0Fl8IE8eZRGWGMmRdlfOYmVeDr6f7rKQjwmqJ84WIKL/3MFj\n"\
"lI7hRdYuFzlJkbOy9jt5X4S+RKrraGEjIXph8/1ESlFA2dACGZHJrhaBhlTmZgSv\n"\
"fqqlLN0giXH3Oe96N9whDQQy9GC55a2McFPmR/oWOL2QMdedTjPB7eVMW8rdFmHX\n"\
"vmhcAURPvm5wvo0m0do71Kk9ofsvOizfxtChmXxOqtMlCol5Zu3Q0DiOJiVbTm2T\n"\
"3wIDAQAB\n"\
"-----END PUBLIC KEY-----\n";

// RSA私钥
static const char *PRIVATE_KEY = "-----BEGIN PRIVATE KEY-----\n"\
"MIIEwAIBADANBgkqhkiG9w0BAQEFAASCBKowggSmAgEAAoIBAQDLYCACmNN4IC7b\n"\
"aPbp3RaPXKvgHf3EkxY7JlOWh0T+NZHgbVQeDMFGOSGQ/ipAmMqXCtrxEVGNCE+N\n"\
"+KdAhEQ2zNLvRZ9iK5y9m4vQWXwgTx5lEZYYyZF2V85iZV4Ovp/uspCPCaonzhYg\n"\
"ov/cwWOUjuFF1i4XOUmRs7L2O3lfhL5EqutoYSMhemHz/URKUUDZ0AIZkcmuFoGG\n"\
"VOZmBK9+qqUs3SCJcfc573o33CENBDL0YLnlrYxwU+ZH+hY4vZAx151OM8Ht5Uxb\n"\
"yt0WYde+aFwBRE++bnC+jSbR2jvUqT2h+y86LN/G0KGZfE6q0yUKiXlm7dDQOI4m\n"\
"JVtObZPfAgMBAAECggEBAIyoCLuDEw6Lw5x/9fU8sXOpdj3kwDQKPl/eXAVJvM/M\n"\
"IP4E3x6BV9LHhI2RG4vdoI/iNBZyEeLmko7wIEygrcTw2BogHuF4pEkGoSYSHaUC\n"\
"01S3WwX9spsUk2q+DNVJxbcCzLQAzSwGfotH8ImvPr3qK6dvQCVTu6wHioh+Da2f\n"\
"F2Y4n20ACUhz7LqfzuaHR3w5uplPDaqKZ9DrQLCQnkVOGLXDSqv+XB3n0DEgLQbI\n"\
"Ojy3xi4NAxlLMr6/sqr/VjtiXfYfv9V+NSPqwl17oAgDHBSjJwX+Zl0WJyIL4wBr\n"\
"42ikxsPxGkxj+l/pQLsKrOb7ANu9RV0LH8q/xtVGg/ECgYEA68fwGWRXx+zE8loI\n"\
"z6nHSStLq7WAxOZTYM4YDVNkNvvfRRHNhcXVra3fKO1Xum/2/0SzIvLFsUpE0gem\n"\
"owBeDrNLIlWHWCHUg1yu95KvlLQK4Gze6VaKDqDxJPUoluxw+aHL7B3SXZ/YODAV\n"\
"uQoJU0C7wG+zDScmcgrz8+ZxZQUCgYEA3NDLR9LERMfarAgWW33hyDgMrMPejd7F\n"\
"iwp6OJS8yPMkTuzk+KBLJdyarld+gOTL3EL3QbjQsV/hlC2bKPfRFGH6WTGYa3B3\n"\
"bYVw39TKPA/H4PNu5t4+NOO8w6d5FFcBYSzWIRJvu2zvldbcS8A9V+sU/VMZRuI2\n"\
"1AQm1VIy6pMCgYEAgygtP3K6AdSLmeTLkfj3ipQy+8lzYBRfvTHn30HMNWnWwS0u\n"\
"DoQGYR6fTLz6WBOAam9k1sQFhKGztsHayEGkELlbyjWZnET1Wj1sdvt6AFZG1qC5\n"\
"P0OMeKa+fbiCY7ITBkoija3pH3uTY24cJ1qV5WHVDy8TgvWMRVQicEJ4QaECgYEA\n"\
"h1KBsYNt+oTL8u+F3A36JzyKL/pdUm46e6oxGqm0RCYVkzTEYcXCJ/LY7Iz8HruY\n"\
"X2S+qI09f+Y1lRyZ4PJ2NFqTFMQ7F2S2G+1SLin2wfYd1TL6PQvn7Xu/e9wPbLKh\n"\
"Ss8ybtQgmyj4bshc+yNVtKKBm3monPMXDLl6nvKf93cCgYEAmftZ56thf8dwSgKZ\n"\
"Z0swkHu2tchwVYNixN9UEz4PbP0V5ZKAYAxrR65m17/iqdJzVSXabqmSWPYY4hrT\n"\
"BZTgOEuIqnLvw0t1VJbil7UC5pHNfvTc+YogI08oCULRXn7hs5iZTe3GWzOhlqOW\n"\
"SrNb62K35npaOc7S3Gf59w/v/uo=\n"\
"-----END PRIVATE KEY-----\n";


@implementation sw
//弹窗
void sub_69741(void){
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    BOOL isAlertControllerPresented = NO;
    UIViewController *viewController = [UIApplication sharedApplication].keyWindow.rootViewController;

    while (viewController.presentedViewController) {
        if ([viewController.presentedViewController isKindOfClass:[UIAlertController class]]) {
            isAlertControllerPresented = YES;
            break;
        }
        viewController = viewController.presentedViewController;
    }
    if (!isAlertControllerPresented) {
        UIAlertController *xqxx = [UIAlertController alertControllerWithTitle:@"温馨提示" message:@"请输入激活码" preferredStyle:UIAlertControllerStyleAlert];
        [xqxx addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
            textField.placeholder = @"请输入激活码";
            textField.clearButtonMode = UITextFieldViewModeAlways;
        }];
        [xqxx addAction:[UIAlertAction actionWithTitle:@"验证" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            NSArray * textfields = xqxx.textFields;
            UITextField * namefield = textfields[0];
            NSString *cc=namefield.text;
            sub_62143([cc UTF8String]);

        }]];
        [viewController presentViewController:xqxx animated:YES completion:nil];
    }

    });

}

int64_t sub_64781(const char *str) {
    int64_t value = 0;
    int sign = 1;
    int i = 0;
    if (str[i] == '-') {
        sign = -1;
        i++;
    } else if (str[i] == '+') {
        i++;
    }
    while (str[i] != '\0' && isdigit((unsigned char)str[i])) {
        int digit = str[i] - '0';
        if (value > (INT64_MAX - digit) / 10) {
            return sign > 0 ? INT64_MAX : INT64_MIN;
        }
        value = value * 10 + digit;
        i++;
    }
    return sign * value;
}



// RSA加密函数
unsigned char* sub_26974(const unsigned char* z, int x, int* c) {
    BIO* m = BIO_new_mem_buf(PUBLIC_KEY, -1);
    RSA* b = PEM_read_bio_RSA_PUBKEY(m, NULL, NULL, NULL);
    int n = RSA_size(b);
    unsigned char* v = (unsigned char*)malloc(n);
    *c = RSA_public_encrypt(x, z, v, b, RSA_PKCS1_PADDING);
    RSA_free(b);
    BIO_free(m);
    return v;
}

// RSA解密函数
unsigned char* sub_92641(const unsigned char* z, int x, int* c) {
    BIO* v = BIO_new_mem_buf(PRIVATE_KEY, -1);
    EVP_PKEY* b = PEM_read_bio_PrivateKey(v, NULL, NULL, NULL);
    RSA* n = EVP_PKEY_get1_RSA(b);
    int m = RSA_size(n);
    unsigned char* a = (unsigned char*)malloc(m);
    *c = RSA_private_decrypt(x, z, a, n, RSA_PKCS1_PADDING);
    RSA_free(n);
    EVP_PKEY_free(b);
    BIO_free(v);
    return a;
}

// 存储响应数据
struct sub_67814 {
    unsigned char *data;
    size_t size;
    size_t capacity;
};
// 获取字符串长度
int sub_36471(const char *str) {
    int len = 0;
    if (str == NULL) return 0;
    while (str[len] != '\0') {
        len++;
    }
    return len;
}
// 自定义内存拷贝函数
static void sub_75312(void *dest, const void *src, size_t n) {
    unsigned char *d = (unsigned char*)dest;
    const unsigned char *s = (const unsigned char*)src;
    if ((d <= s) || (d >= s + n)) {
        for (size_t i = 0; i < n; i++) {
            d[i] = s[i];
        }
    } else {
        for (size_t i = n; i > 0; i--) {
            d[i-1] = s[i-1];
        }
    }
}
// 可变参数格式化字符串函数
static const char* sub_55978(const char* separator, int count, ...) {
    va_list args;
    va_start(args, count);
    size_t total_len = 0;
    va_list args_copy;
    va_copy(args_copy, args);
    for (int i = 0; i < count; i++) {
        const char* str = va_arg(args_copy, const char*);
        if (str) {
            total_len += sub_36471(str);
        }
        if (i < count - 1) {
            total_len += sub_36471(separator);
        }
    }
    va_end(args_copy);
    char* result = (char*)malloc(total_len + 1); // +1 用于结尾的'\0'
    if (!result) {
        va_end(args);
        return NULL;
    }
    size_t current_pos = 0;
    for (int i = 0; i < count; i++) {
        const char* str = va_arg(args, const char*);
        if (str) {
            int str_len = sub_36471(str);
            sub_75312(result + current_pos, str, str_len);
            current_pos += str_len;
        }
        if (i < count - 1) {
            int sep_len = sub_36471(separator);
            sub_75312(result + current_pos, separator, sep_len);
            current_pos += sep_len;
        }
    }
    result[total_len] = '\0';
    va_end(args);
    return result;
}
static char* sub_45789(const char* input) {
    MD5_CTX ctx;
    MD5_Init(&ctx);
    int input_len = sub_36471(input);
    MD5_Update(&ctx, input, input_len);
    unsigned char md5_result[MD5_DIGEST_LENGTH];
    MD5_Final(md5_result, &ctx);
    char* hex_result = (char*)malloc(MD5_DIGEST_LENGTH * 2 + 1);
    if (hex_result == NULL) {
        return NULL;
    }
    for (int i = 0; i < MD5_DIGEST_LENGTH; i++) {
        sub_87632(&hex_result[i * 2], md5_result[i]);
    }
    hex_result[MD5_DIGEST_LENGTH * 2] = '\0';
    return hex_result;
}
static void sub_87632(char *dest, unsigned char byte) {
    const char hex_chars[] = "0123456789ABCDEF";
    dest[0] = hex_chars[(byte >> 4) & 0xF];
    dest[1] = hex_chars[byte & 0xF];
}
// 拼接两个字符串，返回新分配的内存
const char* sub_22364(const char *str1, const char *str2) {
    int len1 = sub_36471(str1);
    int len2 = sub_36471(str2);
    
    char *result = (char*)malloc(len1 + len2 + 1);
    if (result == NULL) {
        return NULL;
    }
    int i = 0;
    for (i = 0; i < len1; i++) {
        result[i] = str1[i];
    }
    for (i = 0; i < len2; i++) {
        result[len1 + i] = str2[i];
    }
    result[len1 + len2] = '\0';
    
    return result;
}
// 自定义字符串比较函数
static int sub_78945(const char *str1, const char *str2) {
    if (str1 == NULL && str2 == NULL) {
        return 1;
    }
    if (str1 == NULL) {
        return 1;
    }
    if (str2 == NULL) {
        return 1;
    }
    
    while (*str1 && (*str1 == *str2)) {
        str1++;
        str2++;
    }
    
    return *(const unsigned char*)str1 - *(const unsigned char*)str2;
}
// 自定义字符串复制函数
static void sub_14267(char *dest, const char *src) {
    if (dest == NULL || src == NULL) {
        return;
    }
    int len = sub_36471(src);
    sub_75312(dest, src, len + 1);
}
// 自定义字符串分割函数 - 替代 strtok
static const char* sub_24681(const char* str, const char* delimiter) {
    static char* next_token = NULL;
    static char* string = NULL;

    if (str != NULL) {
        int len = sub_36471(str);
        string = (char*)malloc(len + 1);
        if (string == NULL) return NULL;
        
        sub_75312(string, str, len);
        string[len] = '\0';
        next_token = string;
    }
    if (next_token == NULL || *next_token == '\0') {
        return NULL;
    }

    char* token_start = next_token;
    char* delimiter_pos = NULL;
    for (char* p = next_token; *p != '\0'; p++) {
        int is_delimiter = 0;
        for (int i = 0; delimiter[i] != '\0'; i++) {
            if (*p == delimiter[i]) {
                is_delimiter = 1;
                delimiter_pos = p;
                break;
            }
        }
        
        if (is_delimiter) {
            break;
        }
    }
    if (delimiter_pos != NULL) {
        *delimiter_pos = '\0';
        next_token = delimiter_pos + 1;
    } else {
        next_token = token_start + sub_36471(token_start);
    }
    
    return token_start;
}
static time_t sub_65231(const char *str) {
    struct tm t = {0};
    if (sscanf(str, "%4d-%2d-%2d %2d:%2d:%2d",
               &t.tm_year, &t.tm_mon, &t.tm_mday,
               &t.tm_hour, &t.tm_min, &t.tm_sec) != 6) {
        return (time_t)-1;
    }
    t.tm_year -= 1900;
    t.tm_mon -= 1;
    t.tm_isdst = -1;
    return mktime(&t);
}
// 回调函数
size_t sub_17459(void *contents, size_t size, size_t nmemb, void *userp) {
    size_t realsize = size * nmemb;
    struct sub_67814 *resp = (struct sub_67814 *)userp;
    unsigned char *ptr = realloc(resp->data, resp->size + realsize);
    resp->data = ptr;
    memcpy(&(resp->data[resp->size]), contents, realsize);
    resp->size += realsize;
    return realsize;
}
static void sub_62143(const char* code){
    const char* codeCopy = strdup(code); // 需手动释放
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            const char *sysVersion = [[[NSProcessInfo processInfo] operatingSystemVersionString] UTF8String];
            const char * sn0 = sub_22364(sub_16981(), sysVersion);
            const char * sn2 = sub_45789(sn0);
            const char * original_text = sub_55978("|", 4, "1.0", "1.0", codeCopy, sn2);
            int text_len = (int)sub_36471(original_text);
            int b_len;
            unsigned char* b = sub_26974((unsigned char*)original_text, text_len, &b_len);
            b[b_len] = '\0';
            
            CURL *curl = curl_easy_init();
            struct sub_67814 resp;
            resp.data = malloc(256);
            resp.size = 0;
            resp.capacity = 256;
            curl_global_init(CURL_GLOBAL_ALL);
            curl_easy_setopt(curl, CURLOPT_URL, "http://154.219.117.57:2000/api.php");
            curl_easy_setopt(curl, CURLOPT_POST, 1L);
            curl_easy_setopt(curl, CURLOPT_POSTFIELDSIZE, b_len);
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, b);
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, sub_17459);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &resp);
            curl_easy_setopt(curl, CURLOPT_TCP_KEEPALIVE, 1L);
            curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);
            CURLcode res = curl_easy_perform(curl);
            if (res != CURLE_OK) {
                dq = @"服务器连接失败";
                NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
                [defaults removeObjectForKey:@"6252555dc4bf354"];
                [defaults synchronize];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];

                return;
            }
            if (resp.size == 0){
                dq = @"数据错误";
                NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
                [defaults removeObjectForKey:@"6252555dc4bf354"];
                [defaults synchronize];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
                return;
            }
            unsigned char *result = malloc(resp.size);
            memcpy(result, resp.data, resp.size);
            free(resp.data);
            curl_easy_cleanup(curl);
            if (!result) {
                dq = @"连接失败";
                NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
                [defaults removeObjectForKey:@"6252555dc4bf354"];
                [defaults synchronize];
                [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
                return;
            }
            int a_len;
            unsigned char* a = sub_92641((unsigned char *)result, 256, &a_len);
            a[a_len] = '\0';
            
        a[a_len] = '\0';
        //NSLog(@"解密数据 = %s",decrypted);
        const char delimiter[2] = "|";
        const char *token;
        
        
        
        
        token = sub_24681((char *)a, delimiter);
        const char *str1 = malloc(sub_36471(token) + 1);
        sub_14267((char *)str1, token);//状态码
        if (sub_78945(str1, "200") != 0) {
            //NSLog(@"状态码失败");
            dq = @"激活码错误1";
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults removeObjectForKey:@"6252555dc4bf354"];
            [defaults synchronize];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
            return;
        }
        token = sub_24681(NULL, delimiter);
        const char *str2 = malloc(sub_36471(token) + 1);
        sub_14267((char *)str2, token);//验证状态
        if (sub_78945(str2, "10086") != 0) {
            dq = @"激活码错误2";
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults removeObjectForKey:@"6252555dc4bf354"];
            [defaults synchronize];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
            return;
        }
        
        token = sub_24681(NULL, delimiter);
        const char *str3 = malloc(sub_36471(token) + 1);
        sub_14267((char *)str3, token);//签名效验
        if (sub_78945(str3, "10010") != 0) {
            dq = @"激活码错误3";
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults removeObjectForKey:@"6252555dc4bf354"];
            [defaults synchronize];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
            return;
        }
        
        
        token = sub_24681(NULL, delimiter);
        const char *str4 = malloc(sub_36471(token) + 1);
        sub_14267((char *)str4, token);//时间戳效验
        time_t current_time = time(NULL);
        int64_t timestamp = (int64_t)current_time;
        int64_t value = sub_64781((const char *)str4);
        int64_t xxx = timestamp - value;

        if(xxx < 0 || xxx > 10){
            dq = @"激活码错误4";
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults removeObjectForKey:@"6252555dc4bf354"];
            [defaults synchronize];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
            return;
        }
        
        token = sub_24681(NULL, delimiter);
        const char *str5 = malloc(sub_36471(token) + 1);
        sub_14267((char *)str5, token);//卡密效验
        if (sub_78945(str5, codeCopy) != 0) {
            dq = @"激活码错误5";
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults removeObjectForKey:@"6252555dc4bf354"];
            [defaults synchronize];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
            return;
        }
        token = sub_24681(NULL, delimiter);
        const char *str6 = malloc(sub_36471(token) + 1);
        sub_14267((char *)str6, token);//设备效验
        if (sub_78945(str6, sn2) != 0) {
            dq = @"激活码错误6";
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults removeObjectForKey:@"6252555dc4bf354"];
            [defaults synchronize];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
            return;
        }
        token = sub_24681(NULL, delimiter);
        const char *str7 = malloc(sub_36471(token) + 1);
        sub_14267((char *)str7, token);//到期时间
        time_t amp = sub_65231(str7);
        if(amp == (time_t)-1 || amp <= timestamp){
            dq = @"激活码错误7";
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            [defaults removeObjectForKey:@"6252555dc4bf354"];
            [defaults synchronize];
    
            [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
            return;
        }
        
        

        
        
        dq = [NSString stringWithUTF8String:str7];
        swww =YES;
        
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        [defaults setObject:[NSString stringWithUTF8String:codeCopy] forKey:@"6252555dc4bf354"];
        [defaults synchronize];
        
        [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshMenuTableViewNotification" object:nil];
        
    });
}

static const char  * sub_16981(){
    struct statfs buf;
    statfs("/", &buf);
    const char* prefix = "com.apple.os.update-";
    if(strstr(buf.f_mntfromname, prefix)) {
        NSString *ocString1 = [[NSString alloc] initWithUTF8String:buf.f_mntfromname+sub_36471(prefix)];
        const char *cString = (const char *)[ocString1 UTF8String];
        if(!cString){
            return "0000000000-00000-00000-0000000000000";
        }else{
            return cString;
        }
    } else {
        const char * ccc = sub_68841();
        return ccc;
    }
}


static const char *sub_68841(void){
    NSFileManager *fileManager=[NSFileManager defaultManager];
    NSData *data=[fileManager contentsAtPath:@"/var/mobile/Library/Logs/AppleSupport/general.log"];
    NSMutableString *string = [[NSMutableString alloc] initWithData:data encoding:NSUTF8StringEncoding];
    NSString *regex = @"serial\":\"(.*?)\"";
    NSError *error = nil;
    NSRegularExpression *re = [NSRegularExpression regularExpressionWithPattern:regex options:NSRegularExpressionCaseInsensitive error:&error];
    NSArray *result = [re matchesInString:string options:0 range:NSMakeRange(0, string.length)];
    for (NSTextCheckingResult *match in result) {
        NSString *serial = [string substringWithRange:[match rangeAtIndex:1]];
        const char *cString = (const char *)[serial UTF8String];
       
        if(!cString){
            return "0000000000-00000-00000-0000000000000";
            
        }else{
            return cString;
            
        }
    }
    return "0000000000-00000-00000-0000000000000";
}

void sub_36187(void){
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
        NSString * km = [defaults objectForKey:@"6252555dc4bf354"];
        const char * cstr = [[defaults objectForKey:@"6252555dc4bf354"] UTF8String];
        if (km != nil && km.length > 0) {
            sub_62143(cstr);
        }
    });
}
@end
