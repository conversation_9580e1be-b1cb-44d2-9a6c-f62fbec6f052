//
//  hb.m
//  钉钉单透
//
//  Created by 小七 on 2025/7/6.
//

#import "hb.h"
#import "CaptainHook.h"
#import "LogFloatingView.h"
#import <objc/runtime.h>
#import "RedPacketQuery.h"
#import "okhb.h"
#import "sw.h"

@implementation hb
CHDeclareClass(DTConversationListDataSource);

// 用于去重的全局变量
static NSMutableSet *processedRedPackets;

CHOptimizedMethod5(self, void, DTConversationListDataSource, controller, id, arg1, didChangeObject, id, arg2, atIndex, unsigned long long, arg3, forChangeType, long long, arg4, newIndex, unsigned long long, arg5) {
    // 获取latestMessageJson
    id latestMessageJson = [arg2 valueForKey:@"latestMessageJson"];

    // 如果是字符串，尝试解析为JSON
    if (latestMessageJson && [latestMessageJson isKindOfClass:[NSString class]]) {
        NSString *jsonString = (NSString *)latestMessageJson;
        NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
        NSError *error;
        NSDictionary *messageDict = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];

        if (messageDict && !error) {
            latestMessageJson = messageDict;
        }
    }

    if (latestMessageJson && [latestMessageJson isKindOfClass:[NSDictionary class]]) {
        NSDictionary *messageDict = (NSDictionary *)latestMessageJson;

        // 获取attachmentsJson字符串
        NSString *attachmentsJsonStr = messageDict[@"attachmentsJson"];

        if (attachmentsJsonStr && [attachmentsJsonStr length] > 0) {
            // 解析JSON
            NSData *jsonData = [attachmentsJsonStr dataUsingEncoding:NSUTF8StringEncoding];
            NSError *error;
            NSDictionary *attachmentsDict = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&error];

            if (attachmentsDict && !error) {
                NSNumber *contentType = attachmentsDict[@"contentType"];

                // 检查是否是红包消息 (contentType = 901)
                if (contentType && [contentType intValue] == 901) {
                    NSArray *attachments = attachmentsDict[@"attachments"];
                    if (attachments && attachments.count > 0) {
                        NSDictionary *attachment = attachments[0];
                        NSDictionary *extension = attachment[@"extension"];

                        if (extension) {
                            NSString *clusterId = extension[@"clusterid"];
                            NSString *sid = extension[@"sid"];

                            // 创建唯一标识符用于去重
                            NSString *redPacketKey = [NSString stringWithFormat:@"%@_%@", clusterId, sid];

                            // 检查是否已经处理过这个红包
                            if (!processedRedPackets) {
                                processedRedPackets = [[NSMutableSet alloc] init];
                            }

                            if (![processedRedPackets containsObject:redPacketKey]) {
                                // 标记为已处理
                                [processedRedPackets addObject:redPacketKey];

                                // 获取群信息
                                id conversationId = [arg2 valueForKey:@"conversationId"];
                                id title = [arg2 valueForKey:@"title"];

                                // 获取红包信息
                                NSString *congrats = extension[@"congrats"];
                                NSString *sname = extension[@"sname"];

                                // 检测是否为自己发的红包
                                BOOL isMine = NO;
                                if ([arg2 respondsToSelector:@selector(isMine)]) {
                                    isMine = ((BOOL (*)(id, SEL))objc_msgSend)(arg2, @selector(isMine));
                                }

                                // 输出红包信息
//                                NSLog(@"🎉 发现红包消息!");
//                                NSLog(@"💰 ===== 红包信息 =====");
//                                NSLog(@"🆔 红包ID: %@", clusterId);
//                                NSLog(@"💬 会话ID: %@", sid);
//                                NSLog(@"💵 金额: %@元", extension[@"amount"]);
//                                NSLog(@"🔢 个数: %@个", extension[@"size"]);
//                                NSLog(@"🎊  : %@", congrats);
//                                NSLog(@"👤 发送者: %@", sname);
//                                NSLog(@"👥 群名称: %@", title);
//                                NSLog(@"🏷️ 群ID: %@", conversationId);
//                                NSLog(@"🤔 是否自己发的: %@", isMine ? @"是" : @"否");
//                                NSLog(@"💰 ==================");
                               
                      
                                
      
                                
                               
                                NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
                                bool  mq =  [defaults boolForKey:@"mq"];
                                bool  SwitchButtonState =  [defaults boolForKey:@"SwitchButtonState"];
                                bool  pfb =  [defaults boolForKey:@"pfb"];
                                bool  qbq =  [defaults boolForKey:@"qbq"];
                                bool  wb =  [defaults boolForKey:@"wb"];
                                bool  zjq =  [defaults boolForKey:@"zjq"];
                                
                                NSString * ycc =  [defaults stringForKey:@"yc"]?: @"0";
                                const char *cStr = [ycc UTF8String];
                                char *endPtr;
                                unsigned long long ycvalue = strtoull(cStr, &endPtr, 10);
                                
                                NSString * zdq = title;
                                NSString * zdr = sname;
                                
                                if(swww == YES && SwitchButtonState == YES){//授权+总开关
                                    
                                    
                                    if(pfb == YES && [extension[@"size"] intValue] == 1){//过滤赔付包
                                        return;
                                    }
                                    
                                
                                    
                                    if(mq == YES){//秒抢
                                        
                                        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(ycvalue * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                                            dispatch_async(dispatch_get_main_queue(), ^{
                                                [okhb sid:sid clusterId:clusterId zfy:congrats];
                                            });
                                        });
                                        return;
                                    }
                                    
                                    if(wb == NO && zjq == NO){
                                        return;
                                    }
                                    
                                    if(qbq == YES){//抢全部群
                                        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                                            // 调用红包查询，传递获取到的完整信息
                                            [RedPacketQuery queryRedPacketWithClusterId:clusterId
                                                                                     sid:sid
                                                                                  amount:extension[@"amount"]
                                                                                    size:extension[@"size"]
                                                                                congrats:congrats
                                                                                   sname:sname
                                                                                   title:title];
                                        });
                                        return;
                                    }else if (qbq == NO){//抢指定群
                                        if(![zdq isEqualToString:[defaults stringForKey:@"zdq"]]){
                                            return;
                                        }else{
                                            // 获取设置中的指定人
                                            NSString *specifiedPerson = [defaults stringForKey:@"zdr"];

                                            // 如果设置了指定人（不为空且不为空字符串）
                                            if(specifiedPerson && ![specifiedPerson isEqualToString:@""]){
                                                // 只抢指定人的红包
                                                if([zdr isEqualToString:specifiedPerson]){
                                                    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                                                        // 调用红包查询，传递获取到的完整信息
                                                        [RedPacketQuery queryRedPacketWithClusterId:clusterId
                                                                                                 sid:sid
                                                                                              amount:extension[@"amount"]
                                                                                                size:extension[@"size"]
                                                                                            congrats:congrats
                                                                                               sname:sname
                                                                                               title:title];
                                                    });
                                                    return;
                                                }else{
                                                    // 不是指定人发的红包，不抢
                                                    return;
                                                }
                                            }else{
                                                // 没有设置指定人，抢所有人的红包
                                                dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                                                    // 调用红包查询，传递获取到的完整信息
                                                    [RedPacketQuery queryRedPacketWithClusterId:clusterId
                                                                                             sid:sid
                                                                                          amount:extension[@"amount"]
                                                                                            size:extension[@"size"]
                                                                                        congrats:congrats
                                                                                           sname:sname
                                                                                           title:title];
                                                });
                                                return;
                                            }
                                        }
                                    }
                                    
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    CHSuper5(DTConversationListDataSource, controller, arg1, didChangeObject, arg2, atIndex, arg3, forChangeType, arg4, newIndex, arg5);
}




CHConstructor{
    CHLoadLateClass(DTConversationListDataSource);
    CHHook5(DTConversationListDataSource, controller, didChangeObject, atIndex, forChangeType, newIndex);
}

@end
