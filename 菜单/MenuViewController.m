//
//  MenuViewController.m
//  钉钉单透
//
//  Created by 小七 on 2025/7/7.
//

#import "MenuViewController.h"
#import "sw.h"
#define CurrentViewSize self.view.frame.size
#define kWidth  [UIScreen mainScreen].bounds.size.width
#define kHeight [UIScreen mainScreen].bounds.size.height

@interface MenuViewController () <UITableViewDataSource, UITableViewDelegate, UITextFieldDelegate>

@property (nonatomic, strong) UITableView *tableView;

@end

@implementation MenuViewController

- (void)viewDidLoad {
    [super viewDidLoad];

    // 设置背景色
    self.view.backgroundColor = [UIColor colorWithRed:239.0/255.0 green:238.0/255.0 blue:242.0/255.0 alpha:1.0];

    // 设置标题
    self.title = @"钉钉天使透3.1";

    // 监听表格刷新通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(refreshTableView)
                                                 name:@"RefreshMenuTableViewNotification"
                                               object:nil];
    if (@available(iOS 13.0, *)) {
        // 创建自定义按钮视图
        UIButton *backButton = [UIButton buttonWithType:UIButtonTypeSystem];
        UIImage *backImage = [UIImage systemImageNamed:@"chevron.left"];
        [backButton setImage:backImage forState:UIControlStateNormal];
        [backButton setTitle:@"返回" forState:UIControlStateNormal];
        [backButton setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
        backButton.titleLabel.font = [UIFont systemFontOfSize:17];
        [backButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
        [backButton sizeToFit];

        UIBarButtonItem *closeButton = [[UIBarButtonItem alloc] initWithCustomView:backButton];
        self.navigationItem.leftBarButtonItem = closeButton;
    } else {
        UIBarButtonItem *closeButton = [[UIBarButtonItem alloc] initWithTitle:@"返回"
                                                                        style:UIBarButtonItemStylePlain
                                                                       target:self
                                                                       action:@selector(closeButtonTapped)];
        self.navigationItem.leftBarButtonItem = closeButton;
    }

    [self setupUI];
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshButtonsNotification" object:nil];
}

- (void)setupUI {
    // 创建 UITableView，设置frame留出左右间距
    CGRect tableFrame = CGRectMake(10, 0, self.view.bounds.size.width - 20, self.view.bounds.size.height);
    self.tableView = [[UITableView alloc] initWithFrame:tableFrame style:UITableViewStyleGrouped];
    self.tableView.dataSource = self;
    self.tableView.delegate = self;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
    self.tableView.separatorColor = [UIColor colorWithRed:0.9 green:0.9 blue:0.9 alpha:1.0];
    self.tableView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.showsHorizontalScrollIndicator = NO;

    // 注册单元格
    [self.tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"MenuCell"];

    // 添加到视图
    [self.view addSubview:self.tableView];
}

- (void)closeButtonTapped {
    [[NSNotificationCenter defaultCenter] postNotificationName:@"StartKeepingButtonsVisibleNotification" object:nil];
    [self dismissViewControllerAnimated:YES completion:^{
        [[NSNotificationCenter defaultCenter] postNotificationName:@"StopKeepingButtonsVisibleNotification" object:nil];
        [[NSNotificationCenter defaultCenter] postNotificationName:@"RefreshButtonsNotification" object:nil];
    }];
}

// 刷新表格方法
- (void)refreshTableView {
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.tableView reloadData];
    });
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - UITableView DataSource

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return 6; // 2个分组
}

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    if (section == 0) {
        return 1; // 第一个分组1个单元格
    }else if (section == 1) {
       return 1;
   }else if (section == 2) {
        return 4;
    } else if (section == 3) {
        return 3;
    } else if (section == 4) {
        return 3;
    } else if (section == 5) {
        return 4;
    }
    return 0;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleValue1 reuseIdentifier:@"MenuCell"];
    cell.backgroundColor = [UIColor clearColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    

    if (indexPath.section == 0) {
        // 第一个分组：授权管理
        cell.textLabel.text = @"到期时间";
        cell.detailTextLabel.text = dq;
        cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
    }else if (indexPath.section == 1) {
        // 第一个分组：功能开关
        cell.textLabel.text = @"功能开关";
        UISwitch *switchControl = [[UISwitch alloc] init];
        switchControl.tag = 999;
       
        switchControl.on = [defaults boolForKey:@"SwitchButtonState"];
        cell.accessoryView = switchControl;
        [switchControl addTarget:self action:@selector(switchValueChanged:) forControlEvents:UIControlEventValueChanged];
    }else if (indexPath.section == 2) {
        // 第二个分组：基础设置
        if (indexPath.row == 0) {
            cell.textLabel.text = @"语音播报";
            UISwitch *switchControl = [[UISwitch alloc] init];
            switchControl.tag = 1001;
            switchControl.on = [defaults boolForKey:@"yy"];
            cell.accessoryView = switchControl;
            [switchControl addTarget:self action:@selector(switchValueChanged:) forControlEvents:UIControlEventValueChanged];
        } else if (indexPath.row == 1) {
            cell.textLabel.text = @"抢包延迟";
            cell.detailTextLabel.text = [NSString stringWithFormat:@"%@秒",[defaults stringForKey:@"yc"]?: @"0"];
            cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
        } else if (indexPath.row == 2) {
            cell.textLabel.text = @"记忆时间";
            cell.detailTextLabel.text = [NSString stringWithFormat:@"%@秒",[defaults stringForKey:@"jy"]?: @"60"];
            cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
        } else if (indexPath.row == 3) {
            cell.textLabel.text = @"查询速度";
            cell.detailTextLabel.text = [NSString stringWithFormat:@"%@豪秒",[defaults stringForKey:@"cx"]?: @"150"];
            cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
        }
    } else if (indexPath.section == 3) {
        // 第三个分组：指定人和群
        if (indexPath.row == 0) {
            cell.textLabel.text = @"抢所有群";
            UISwitch *switchControl = [[UISwitch alloc] init];
            switchControl.tag = 1002;
            switchControl.on = [defaults boolForKey:@"qbq"];
            cell.accessoryView = switchControl;
            [switchControl addTarget:self action:@selector(switchValueChanged:) forControlEvents:UIControlEventValueChanged];
        } else if (indexPath.row == 1) {
            cell.textLabel.text = @"抢指定群";
            cell.detailTextLabel.text = [NSString stringWithFormat:@"%@",[defaults stringForKey:@"zdq"]?: @"未指定"];
            cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
        }else if (indexPath.row == 2) {
            cell.textLabel.text = @"抢指定人";
            cell.detailTextLabel.text = [NSString stringWithFormat:@"%@",[defaults stringForKey:@"zdr"]?: @"未指定"];
            cell.accessoryType = UITableViewCellAccessoryDisclosureIndicator;
        }
    } else if (indexPath.section == 4) {
        // 第四个分组：抢包范围
        if (indexPath.row == 0) {
            cell.textLabel.text = @"秒抢";
            UISwitch *switchControl = [[UISwitch alloc] init];
            switchControl.tag = 1003;
            switchControl.on = [defaults boolForKey:@"mq"];
            cell.accessoryView = switchControl;
            [switchControl addTarget:self action:@selector(switchValueChanged:) forControlEvents:UIControlEventValueChanged];
        }else if (indexPath.row == 1) {
            cell.textLabel.text = @"抢自己";
            UISwitch *switchControl = [[UISwitch alloc] init];
            switchControl.tag = 1005;
            switchControl.on = [defaults boolForKey:@"zj"];
            cell.accessoryView = switchControl;
            [switchControl addTarget:self action:@selector(switchValueChanged:) forControlEvents:UIControlEventValueChanged];
        }else if (indexPath.row == 2) {
            cell.textLabel.text = @"过滤赔付包";
            UISwitch *switchControl = [[UISwitch alloc] init];
            switchControl.tag = 888;
            switchControl.on = [defaults boolForKey:@"pfb"];
            cell.accessoryView = switchControl;
            [switchControl addTarget:self action:@selector(switchValueChanged:) forControlEvents:UIControlEventValueChanged];
        }
    } else if (indexPath.section == 5) {
        // 第五个分组：玩法设置
        if (indexPath.row == 0) {
            cell.textLabel.text = @"雷类型";
            NSArray *array = [NSArray arrayWithObjects:@"单雷",@"多雷",@"智能雷", nil];
            UISegmentedControl *segment = [[UISegmentedControl alloc]initWithItems:array];

            // 读取保存的值并设置选择器状态
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            NSString *savedValue = [defaults objectForKey:@"dlx"];
            if ([savedValue isEqualToString:@"0"]) {
                segment.selectedSegmentIndex = 0; // 单雷
            } else if ([savedValue isEqualToString:@"1"]) {
                segment.selectedSegmentIndex = 1; // 多雷
            } else if ([savedValue isEqualToString:@"2"]) {
                segment.selectedSegmentIndex = 2; // 智能雷
            } else {
                segment.selectedSegmentIndex = 0; // 默认选择第一个
            }

            segment.tag = 2001; // 雷类型选择器
            segment.apportionsSegmentWidthsByContent = YES;
            [segment addTarget:self action:@selector(segmentValueChanged:) forControlEvents:UIControlEventValueChanged];
            cell.accessoryView = segment;
            
            
        } else if (indexPath.row == 1) {
            cell.textLabel.text = @"雷位置";
            NSArray *array = [NSArray arrayWithObjects:@"分为雷",@"角为雷",@"元为雷", nil];
            UISegmentedControl *segment = [[UISegmentedControl alloc]initWithItems:array];

            // 读取保存的值并设置选择器状态
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            NSString *savedValue = [defaults objectForKey:@"lwz"];
            if ([savedValue isEqualToString:@"0"]) {
                segment.selectedSegmentIndex = 0; // 分为雷
            } else if ([savedValue isEqualToString:@"1"]) {
                segment.selectedSegmentIndex = 1; // 角为雷
            } else if ([savedValue isEqualToString:@"2"]) {
                segment.selectedSegmentIndex = 2; // 元为雷
            } else {
                segment.selectedSegmentIndex = 0; // 默认选择第一个
            }

            segment.tag = 2002; // 雷位置选择器
            segment.apportionsSegmentWidthsByContent = YES;
            [segment addTarget:self action:@selector(segmentValueChanged:) forControlEvents:UIControlEventValueChanged];
            cell.accessoryView = segment;
        }else if (indexPath.row == 2) {
            cell.textLabel.text = @"只抢尾包";
            UISwitch *switchControl = [[UISwitch alloc] init];
            switchControl.tag = 1004;
            switchControl.on = [defaults boolForKey:@"wb"];
            cell.accessoryView = switchControl;
            [switchControl addTarget:self action:@selector(switchValueChanged:) forControlEvents:UIControlEventValueChanged];
        }else if (indexPath.row == 3) {
            cell.textLabel.text = @"中间抢+扫尾";
            UISwitch *switchControl = [[UISwitch alloc] init];
            switchControl.tag = 1006;
            switchControl.on = [defaults boolForKey:@"zjq"];
            cell.accessoryView = switchControl;
            [switchControl addTarget:self action:@selector(switchValueChanged:) forControlEvents:UIControlEventValueChanged];
        }
        

    }

    return cell;
}

#pragma mark - UITableView Delegate

- (void)tableView:(UITableView *)tableView willDisplayCell:(UITableViewCell *)cell forRowAtIndexPath:(NSIndexPath *)indexPath
{
    // 设置cell的背景色为透明，如果不设置这个的话，则原来的背景色不会被覆盖
    cell.backgroundColor = UIColor.clearColor;
    
    // 圆角弧度半径
    CGFloat cornerRadius = 10.0f;
    
    // 创建一个shapeLayer
    CAShapeLayer *layer = [[CAShapeLayer alloc] init];
    // 显示选中
  //  CAShapeLayer *backgroundLayer = [[CAShapeLayer alloc] init];
    CGMutablePathRef pathRef = CGPathCreateMutable();

    CGRect bounds = CGRectInset(cell.bounds, 0, 0);

    NSInteger rows = [tableView numberOfRowsInSection:indexPath.section];
    BOOL addLine = NO;
    if (rows == 1) {
        // 初始起点为cell的左侧中间坐标
        CGPathMoveToPoint(pathRef, nil, CGRectGetMinX(bounds), CGRectGetMidY(bounds));
        CGPathAddArcToPoint(pathRef, nil, CGRectGetMinX(bounds), CGRectGetMinY(bounds), CGRectGetMidX(bounds), CGRectGetMinY(bounds), cornerRadius);
        
        CGPathAddArcToPoint(pathRef, nil, CGRectGetMaxX(bounds), CGRectGetMinY(bounds), CGRectGetMaxX(bounds), CGRectGetMidY(bounds), cornerRadius);
        
        CGPathAddArcToPoint(pathRef, nil, CGRectGetMaxX(bounds), CGRectGetMaxY(bounds), CGRectGetMinX(bounds), CGRectGetMaxY(bounds), cornerRadius);
        
        CGPathAddArcToPoint(pathRef, nil, CGRectGetMinX(bounds), CGRectGetMaxY(bounds), CGRectGetMinX(bounds), CGRectGetMidY(bounds), cornerRadius);

        CGPathAddLineToPoint(pathRef, nil, CGRectGetMinX(bounds), CGRectGetMidY(bounds));
    } else if (indexPath.row == 0) {
        CGPathMoveToPoint(pathRef, nil, CGRectGetMinX(bounds), CGRectGetMaxY(bounds));
        CGPathAddArcToPoint(pathRef, nil, CGRectGetMinX(bounds), CGRectGetMinY(bounds), CGRectGetMidX(bounds), CGRectGetMinY(bounds), cornerRadius);
        
        CGPathAddArcToPoint(pathRef, nil, CGRectGetMaxX(bounds), CGRectGetMinY(bounds), CGRectGetMaxX(bounds), CGRectGetMidY(bounds), cornerRadius);
        CGPathAddLineToPoint(pathRef, nil, CGRectGetMaxX(bounds), CGRectGetMaxY(bounds));
        addLine = YES;
    } else if (indexPath.row == [tableView numberOfRowsInSection:indexPath.section] - 1) {
        CGPathMoveToPoint(pathRef, nil, CGRectGetMinX(bounds), CGRectGetMinY(bounds));
        
        CGPathAddArcToPoint(pathRef, nil, CGRectGetMinX(bounds), CGRectGetMaxY(bounds), CGRectGetMidX(bounds), CGRectGetMaxY(bounds), cornerRadius);
        
        CGPathAddArcToPoint(pathRef, nil, CGRectGetMaxX(bounds), CGRectGetMaxY(bounds), CGRectGetMaxX(bounds), CGRectGetMidY(bounds), cornerRadius);
        CGPathAddLineToPoint(pathRef, nil, CGRectGetMaxX(bounds), CGRectGetMinY(bounds));
    } else {
        // 添加cell的rectangle信息到path中（不包括圆角）
        CGPathAddRect(pathRef, nil, bounds);
        addLine = YES;
    }
    
    // 把已经绘制好的可变图像路径赋值给图层，然后图层根据这图像path进行图像渲染render
    layer.path = pathRef;
  //  backgroundLayer.path = pathRef;
    
    // 注意：但凡通过Quartz2D中带有creat/copy/retain方法创建出来的值都必须要释放
    CFRelease(pathRef);
    
    // 按照shape layer的path填充颜色，类似于渲染render
    layer.fillColor = [UIColor whiteColor].CGColor;
    
    // view大小与cell一致
    UIView *roundView = [[UIView alloc] initWithFrame:bounds];
    
    // 添加自定义圆角后的图层到roundView中
    [roundView.layer insertSublayer:layer atIndex:0];
    roundView.backgroundColor = UIColor.clearColor;
    
    // cell的背景view
    cell.backgroundView = roundView;
    
    // 添加分割线
    if (addLine == YES) {
        
        CALayer *lineLayer = [[CALayer alloc] init];
        
        CGFloat lineHeight = (1.f / [UIScreen mainScreen].scale);
        
        lineLayer.frame = CGRectMake(18, bounds.size.height-lineHeight, bounds.size.width, lineHeight);
        
        lineLayer.backgroundColor = tableView.separatorColor.CGColor;
        
        [layer addSublayer:lineLayer];
        
    }
    
    // 以上方法存在缺陷当点击cell时还是出现cell方形效果，因此还需要添加以下方法
    // 如果你 cell 已经取消选中状态的话,那以下方法是不需要的.
    UIView *selectedBackgroundView = [[UIView alloc] initWithFrame:bounds];
   // backgroundLayer.fillColor = tableView.separatorColor.CGColor;
  //  [selectedBackgroundView.layer insertSublayer:backgroundLayer atIndex:0];
    selectedBackgroundView.backgroundColor = UIColor.clearColor;
    cell.selectedBackgroundView = selectedBackgroundView;
    
    
    
}


- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension; // 使用系统默认高度
}

- (NSString *)tableView:(UITableView *)tableView titleForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return @"授权管理";
    } else if (section == 1) {
        return @"功能开关";
    } else if (section == 2) {
        return @"基础设置";
    } else if (section == 3) {
        return @"指定人和群";
    } else if (section == 4) {
        return @"抢包范围";
    } else if (section == 5) {
        return @"玩法设置";
    }
    
    return nil; // 其他section不显示标题
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    if (section == 0) {
        return 40; // 分组头部高度
    }
    return 20;
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    if (section == 5) { // 第6个分组（索引为5）
        UIView *footerView = [[UIView alloc] init];
        footerView.backgroundColor = [UIColor clearColor];

        UILabel *label = [[UILabel alloc] init];
        label.text = @"内部测试工具，使用完自行删除，非法用途责任自行负责";
        label.font = [UIFont systemFontOfSize:12];
        label.textColor = [UIColor grayColor];
        label.textAlignment = NSTextAlignmentCenter;
        label.numberOfLines = 0;
        label.translatesAutoresizingMaskIntoConstraints = NO;

        [footerView addSubview:label];

        // 设置约束，左右间距与分组标题一致（16像素）
        [NSLayoutConstraint activateConstraints:@[
            [label.leadingAnchor constraintEqualToAnchor:footerView.leadingAnchor constant:16],
            [label.trailingAnchor constraintEqualToAnchor:footerView.trailingAnchor constant:-16],
            [label.topAnchor constraintEqualToAnchor:footerView.topAnchor constant:8],
            [label.bottomAnchor constraintEqualToAnchor:footerView.bottomAnchor constant:-8]
        ]];

        return footerView;
    }
    return nil;
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    if (section == 5) {
        return 60; // 第6个分组底部高度，为文字留出空间
    }
    return 20; // 其他分组底部高度
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];

    // 获取点击的分组和行
    NSInteger section = indexPath.section;
    NSInteger row = indexPath.row;

   // NSLog(@"点击了第 %ld 个分组的第 %ld 行", (long)section, (long)row);
    if(section == 0 && row == 0){
        sub_69741();
    }else if (section == 2 && row == 1){
        [self cong:@"抢包延迟" msg:@"设置抢包延迟多少秒" tag:@"yc"];
    }else if (section == 2 && row == 2){
        [self cong:@"记忆时间" msg:@"设置记忆时间多少秒" tag:@"jy"];
    }else if (section == 2 && row == 3){
        [self cong:@"查询速度" msg:@"设置查询速度多少豪秒" tag:@"cx"];
    }


}

- (void)switchValueChanged:(UISwitch *)sender {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    if (sender.tag == 999) {
        if(sender.isOn){
            [defaults setBool:YES forKey:@"SwitchButtonState"];
            [defaults synchronize];

            [[NSNotificationCenter defaultCenter] postNotificationName:@"UpdateButtonTitleNotification" object:nil];
        }else{
            [defaults setBool:NO forKey:@"SwitchButtonState"];
            [defaults synchronize];
            [[NSNotificationCenter defaultCenter] postNotificationName:@"UpdateButtonTitleNotification" object:nil];
        }
    }else if (sender.tag == 1001) {
        if(sender.isOn){
            [defaults setBool:YES forKey:@"yy"];
            [defaults synchronize];
       

        }else{
            [defaults setBool:NO forKey:@"yy"];
            [defaults synchronize];
      

        }
    }else if (sender.tag == 1002) {
        if(sender.isOn){
            [defaults setBool:YES forKey:@"qbq"];
            [defaults synchronize];
 

        }else{
            [defaults setBool:NO forKey:@"qbq"];
            [defaults synchronize];
         

        }
    }else if (sender.tag == 1003) {
        if(sender.isOn){
            [defaults setBool:YES forKey:@"mq"];
            [defaults synchronize];
       

        }else{
            [defaults setBool:NO forKey:@"mq"];
            [defaults synchronize];
          

        }
    }else if (sender.tag == 1004) {
        if(sender.isOn){
            [defaults setBool:YES forKey:@"wb"];
            [defaults setBool:NO forKey:@"zjq"];
            [defaults synchronize];
          
            
            // 关闭其他相关开关的UI状态（带动画效果）
            for (NSInteger section = 0; section < [self.tableView numberOfSections]; section++) {
                for (NSInteger row = 0; row < [self.tableView numberOfRowsInSection:section]; row++) {
                    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row inSection:section];
                    UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:indexPath];
                    if (cell && [cell.accessoryView isKindOfClass:[UISwitch class]]) {
                        UISwitch *switchControl = (UISwitch *)cell.accessoryView;
                        if (switchControl.tag == 1006 || switchControl.tag == 1007) {
                            [switchControl setOn:NO animated:YES];
                        }
                    }
                }
            }

        }else{
            [defaults setBool:NO forKey:@"wb"];
            [defaults synchronize];
   
        }
    }else if (sender.tag == 1005) {
        if(sender.isOn){
            [defaults setBool:YES forKey:@"zj"];
            [defaults synchronize];
      

        }else{
            [defaults setBool:NO forKey:@"zj"];
            [defaults synchronize];
    

        }
    }else if (sender.tag == 1006) {
        if(sender.isOn){
            [defaults setBool:YES forKey:@"zjq"];
            [defaults setBool:NO forKey:@"wb"];
            [defaults synchronize];
       
            // 关闭其他相关开关的UI状态（带动画效果）
            for (NSInteger section = 0; section < [self.tableView numberOfSections]; section++) {
                for (NSInteger row = 0; row < [self.tableView numberOfRowsInSection:section]; row++) {
                    NSIndexPath *indexPath = [NSIndexPath indexPathForRow:row inSection:section];
                    UITableViewCell *cell = [self.tableView cellForRowAtIndexPath:indexPath];
                    if (cell && [cell.accessoryView isKindOfClass:[UISwitch class]]) {
                        UISwitch *switchControl = (UISwitch *)cell.accessoryView;
                        if (switchControl.tag == 1004 || switchControl.tag == 1007) {
                            [switchControl setOn:NO animated:YES];
                        }
                    }
                }
            }

        }else{
            [defaults setBool:NO forKey:@"zjq"];
            [defaults synchronize];
          

        }
    }else if (sender.tag == 888) {
        if(sender.isOn){
            [defaults setBool:YES forKey:@"pfb"];
            [defaults synchronize];
      

        }else{
            [defaults setBool:NO forKey:@"pfb"];
            [defaults synchronize];
    

        }
    }
 
}

- (void)segmentValueChanged:(UISegmentedControl *)sender {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];

    if (sender.tag == 2001) {
        // 雷类型选择器
        NSInteger selectedIndex = sender.selectedSegmentIndex;
        if (selectedIndex == 0) {
            [defaults setObject:@"0" forKey:@"dlx"];
        } else if (selectedIndex == 1) {
            [defaults setObject:@"1" forKey:@"dlx"];
        } else if (selectedIndex == 2) {
            [defaults setObject:@"2" forKey:@"dlx"];
        }
    } else if (sender.tag == 2002) {
        // 雷位置选择器
        NSInteger selectedIndex = sender.selectedSegmentIndex;
        if (selectedIndex == 0) {
            [defaults setObject:@"0" forKey:@"lwz"];
        } else if (selectedIndex == 1) {
            [defaults setObject:@"1" forKey:@"lwz"];
        } else if (selectedIndex == 2) {
            [defaults setObject:@"2" forKey:@"lwz"];
        }
    }

    [defaults synchronize];
}


-(void)cong:(NSString *)str msg:(NSString *)msg tag:(NSString *)tag{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
    BOOL isAlertControllerPresented = NO;
    UIViewController *viewController = [UIApplication sharedApplication].keyWindow.rootViewController;

    while (viewController.presentedViewController) {
        if ([viewController.presentedViewController isKindOfClass:[UIAlertController class]]) {
            isAlertControllerPresented = YES;
            break;
        }
        viewController = viewController.presentedViewController;
    }
    if (!isAlertControllerPresented) {
        UIAlertController *xqxx = [UIAlertController alertControllerWithTitle:str message:@"" preferredStyle:UIAlertControllerStyleAlert];
        [xqxx addTextFieldWithConfigurationHandler:^(UITextField * _Nonnull textField) {
            textField.placeholder = msg;
            textField.clearButtonMode = UITextFieldViewModeAlways;
            textField.keyboardType = UIKeyboardTypeNumberPad; // 只显示数字键盘
            textField.delegate = self; // 设置代理
        }];
        [xqxx addAction:[UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
            NSArray * textfields = xqxx.textFields;
            UITextField * namefield = textfields[0];
            NSString *cc=namefield.text;

            
            
            if(cc.length <= 0){
                return;
            }
            NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
            if ([tag isEqualToString:@"yc"]) {
                [defaults setObject:cc forKey:tag];
                [defaults synchronize];
            }else if ([tag isEqualToString:@"jy"]) {
                [defaults setObject:cc forKey:tag];
                [defaults synchronize];
            }else if ([tag isEqualToString:@"cx"]) {
                [defaults setObject:cc forKey:tag];
                [defaults synchronize];
            }
            
            
            // 立即刷新表格
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.tableView reloadData];
            });
            
        }]];
        [viewController presentViewController:xqxx animated:YES completion:nil];
    }

    });
}

#pragma mark - UITextFieldDelegate

// 限制只能输入数字
- (BOOL)textField:(UITextField *)textField shouldChangeCharactersInRange:(NSRange)range replacementString:(NSString *)string {
    // 允许删除操作
    if (string.length == 0) {
        return YES;
    }

    // 只允许输入数字
    NSCharacterSet *numbersOnly = [NSCharacterSet characterSetWithCharactersInString:@"0123456789"];
    NSCharacterSet *characterSetFromTextField = [NSCharacterSet characterSetWithCharactersInString:string];

    return [numbersOnly isSupersetOfSet:characterSetFromTextField];
}

@end
