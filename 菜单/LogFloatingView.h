//
//  LogFloatingView.h
//  钉钉单透
//
//  Created by 小七 on 2025/7/7.
//

#import <UIKit/UIKit.h>

@interface LogFloatingView : UIView

+ (instancetype)shared;
- (void)show;
- (void)hide;
- (void)toggle;
- (BOOL)isVisible;
- (void)addLogMessage:(NSString *)message;
- (void)addRedPacketInfo:(NSDictionary *)redPacketInfo; // 添加红包信息
- (void)updateGrabResult:(NSString *)grabResult forRedPacketId:(NSString *)redPacketId; // 更新抢包结果
- (void)clearLogs; // 清理所有日志

@end
