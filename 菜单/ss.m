//
//  ss.m
//  钉钉单透
//
//  Created by 小七 on 2025/7/7.
//

#import "ss.h"
#import "CaptainHook.h"
#import <UIKit/UIKit.h>
#import <objc/runtime.h>
#import "MenuViewController.h"
#import "LogFloatingView.h"
#import "sw.h"
static bool rz = NO;

// 创建一个简单的按钮处理类
@interface ButtonHandler : NSObject
@property (nonatomic, strong) UIButton *switchButton;
@property (nonatomic, strong) UIButton *menuButton;
@property (nonatomic, strong) UIButton *logButton;
@property (nonatomic, strong) UIButton *hideButton;
@property (nonatomic, strong) NSTimer *keepVisibleTimer;
+ (instancetype)shared;
- (void)circleButtonTapped:(UIButton *)sender;
- (void)openButtonTapped:(UIButton *)sender;
- (void)logButtonTapped:(UIButton *)sender;
- (void)hideButtonTapped:(UIButton *)sender;
- (BOOL)getSwitchState;
- (void)setSwitchState:(BOOL)state;
- (void)updateButtonTitle;
- (void)refreshButtonsDisplay;
- (void)startKeepingButtonsVisible;
- (void)stopKeepingButtonsVisible;
@end

@implementation ButtonHandler
+ (instancetype)shared {
    static ButtonHandler *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[ButtonHandler alloc] init];
        // 监听刷新按钮通知
        [[NSNotificationCenter defaultCenter] addObserver:instance
                                                 selector:@selector(refreshButtonsDisplay)
                                                     name:@"RefreshButtonsNotification"
                                                   object:nil];
        // 监听开始保持按钮可见的通知
        [[NSNotificationCenter defaultCenter] addObserver:instance
                                                 selector:@selector(startKeepingButtonsVisible)
                                                     name:@"StartKeepingButtonsVisibleNotification"
                                                   object:nil];
        // 监听停止保持按钮可见的通知
        [[NSNotificationCenter defaultCenter] addObserver:instance
                                                 selector:@selector(stopKeepingButtonsVisible)
                                                     name:@"StopKeepingButtonsVisibleNotification"
                                                   object:nil];
        // 监听更新按钮标题的通知
        [[NSNotificationCenter defaultCenter] addObserver:instance
                                                 selector:@selector(updateButtonTitle)
                                                     name:@"UpdateButtonTitleNotification"
                                                   object:nil];
    });
    return instance;
}

- (void)circleButtonTapped:(UIButton *)sender {


    // 获取当前的根视图控制器
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if (!keyWindow) {
        keyWindow = [UIApplication sharedApplication].windows.firstObject;
    }
    UIViewController *rootViewController = keyWindow.rootViewController;

    // 找到最顶层的视图控制器
    UIViewController *topViewController = rootViewController;
    while (topViewController.presentedViewController) {
        topViewController = topViewController.presentedViewController;
    }

    // 创建菜单视图控制器
    MenuViewController *menuVC = [[MenuViewController alloc] init];
    UINavigationController *navController = [[UINavigationController alloc] initWithRootViewController:menuVC];

    // 设置为全屏模态展示
    navController.modalPresentationStyle = UIModalPresentationFullScreen;

    // 模态展示菜单页面
    [topViewController presentViewController:navController animated:YES completion:nil];
}

- (void)openButtonTapped:(UIButton *)sender {
    // 切换状态
    BOOL currentState = [self getSwitchState];
    [self setSwitchState:!currentState];
    [self updateButtonTitle];

  
}

- (void)logButtonTapped:(UIButton *)sender {


    // 切换日志悬浮窗显示状态
    [[LogFloatingView shared] toggle];

    // 如果是打开状态，添加一些示例日志
    if ([[LogFloatingView shared] isVisible]) {
        if(rz == NO){
            rz = YES;
           // [[LogFloatingView shared] addLogMessage:@"系统运行正常"];
        }
    }
}

- (void)hideButtonTapped:(UIButton *)sender {
    // 隐藏所有按钮
    if (self.menuButton) {
        self.menuButton.hidden = YES;
    }
    if (self.switchButton) {
        self.switchButton.hidden = YES;
    }
    if (self.logButton) {
        self.logButton.hidden = YES;
    }
    if (self.hideButton) {
        self.hideButton.hidden = YES;
    }

    // 停止保持按钮可见的定时器
    [self stopKeepingButtonsVisible];
}

// 获取开关状态
- (BOOL)getSwitchState {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    return [defaults boolForKey:@"SwitchButtonState"];
}

// 设置开关状态
- (void)setSwitchState:(BOOL)state {
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    [defaults setBool:state forKey:@"SwitchButtonState"];
    [defaults synchronize];
}

// 更新按钮标题
- (void)updateButtonTitle {
    if (self.switchButton) {
        NSString *title = [self getSwitchState] ? @"开" : @"关";
        [self.switchButton setTitle:title forState:UIControlStateNormal];
    }
}

// 刷新按钮显示
- (void)refreshButtonsDisplay {
    dispatch_async(dispatch_get_main_queue(), ^{
        // 获取主窗口
        UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
        if (!keyWindow) {
            keyWindow = [UIApplication sharedApplication].windows.firstObject;
        }

        // 强制确保所有按钮都在窗口最顶层且可见
        if (self.menuButton) {
            self.menuButton.hidden = NO;
            self.menuButton.alpha = 1.0;
            [keyWindow bringSubviewToFront:self.menuButton];
        }
        if (self.switchButton) {
            self.switchButton.hidden = NO;
            self.switchButton.alpha = 1.0;
            [keyWindow bringSubviewToFront:self.switchButton];
        }
        if (self.logButton) {
            self.logButton.hidden = NO;
            self.logButton.alpha = 1.0;
            [keyWindow bringSubviewToFront:self.logButton];
        }
        if (self.hideButton) {
            self.hideButton.hidden = NO;
            self.hideButton.alpha = 1.0;
            [keyWindow bringSubviewToFront:self.hideButton];
        }
    });
}

// 开始保持按钮可见
- (void)startKeepingButtonsVisible {
    [self stopKeepingButtonsVisible]; // 先停止之前的定时器
    self.keepVisibleTimer = [NSTimer scheduledTimerWithTimeInterval:0.05
                                                             target:self
                                                           selector:@selector(refreshButtonsDisplay)
                                                           userInfo:nil
                                                            repeats:YES];
}

// 停止保持按钮可见
- (void)stopKeepingButtonsVisible {
    if (self.keepVisibleTimer) {
        [self.keepVisibleTimer invalidate];
        self.keepVisibleTimer = nil;
    }
}

- (void)dealloc {
    [self stopKeepingButtonsVisible];
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
@end

@implementation ss
CHDeclareClass(DTConversationListController)
CHOptimizedMethod0(self, void, DTConversationListController, viewDidLoad) {
    CHSuper0(DTConversationListController, viewDidLoad);

    

    // 获取主窗口
    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if (!keyWindow) {
        keyWindow = [UIApplication sharedApplication].windows.firstObject;
    }

    // 创建圆形按钮
    UIButton *circleButton = [UIButton buttonWithType:UIButtonTypeCustom];
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
    CGFloat screenHeight = [UIScreen mainScreen].bounds.size.height;
    circleButton.frame = CGRectMake(screenWidth - 70, screenHeight - 400, 50, 50);
    circleButton.backgroundColor = [UIColor colorWithRed:0.2 green:0.2 blue:0.2 alpha:1.0]; // 深灰色背景
    circleButton.layer.cornerRadius = 25; // 半径为宽度的一半，形成圆形
    circleButton.layer.masksToBounds = YES;
    circleButton.layer.borderWidth = 2.0; // 边框宽度
    circleButton.layer.borderColor = [UIColor colorWithRed:1.0 green:0.84 blue:0.0 alpha:1.0].CGColor; // 金色边框
    [circleButton setTitle:@"菜单" forState:UIControlStateNormal];
    [circleButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    circleButton.titleLabel.font = [UIFont boldSystemFontOfSize:15];

    // 添加点击事件
    [circleButton addTarget:[ButtonHandler shared] action:@selector(circleButtonTapped:) forControlEvents:UIControlEventTouchUpInside];

    // 保存按钮引用
    [ButtonHandler shared].menuButton = circleButton;

    // 添加到窗口
    [keyWindow addSubview:circleButton];

    // 创建"开关"按钮
    UIButton *openButton = [UIButton buttonWithType:UIButtonTypeCustom];
    openButton.frame = CGRectMake(screenWidth - 70, screenHeight - 340, 50, 50);
    openButton.backgroundColor = [UIColor colorWithRed:0.2 green:0.2 blue:0.2 alpha:1.0]; // 深灰色背景
    openButton.layer.cornerRadius = 25; // 半径为宽度的一半，形成圆形
    openButton.layer.masksToBounds = YES;
    openButton.layer.borderWidth = 2.0; // 边框宽度
    openButton.layer.borderColor = [UIColor colorWithRed:1.0 green:0.84 blue:0.0 alpha:1.0].CGColor; // 金色边框
    [openButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    openButton.titleLabel.font = [UIFont boldSystemFontOfSize:20];
    [openButton addTarget:[ButtonHandler shared] action:@selector(openButtonTapped:) forControlEvents:UIControlEventTouchUpInside];

    // 保存按钮引用并设置初始状态
    [ButtonHandler shared].switchButton = openButton;
    [[ButtonHandler shared] updateButtonTitle];

    [keyWindow addSubview:openButton];

    // 创建"日志"按钮
    UIButton *logButton = [UIButton buttonWithType:UIButtonTypeCustom];
    logButton.frame = CGRectMake(screenWidth - 70, screenHeight - 280, 50, 50);
    logButton.backgroundColor = [UIColor colorWithRed:0.2 green:0.2 blue:0.2 alpha:1.0]; // 深灰色背景
    logButton.layer.cornerRadius = 25; // 半径为宽度的一半，形成圆形
    logButton.layer.masksToBounds = YES;
    logButton.layer.borderWidth = 2.0; // 边框宽度
    logButton.layer.borderColor = [UIColor colorWithRed:1.0 green:0.84 blue:0.0 alpha:1.0].CGColor; // 金色边框
    [logButton setTitle:@"日志" forState:UIControlStateNormal];
    [logButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    logButton.titleLabel.font = [UIFont boldSystemFontOfSize:15];
    [logButton addTarget:[ButtonHandler shared] action:@selector(logButtonTapped:) forControlEvents:UIControlEventTouchUpInside];

    // 保存按钮引用
    [ButtonHandler shared].logButton = logButton;

    [keyWindow addSubview:logButton];

    // 创建"隐"按钮
    UIButton *hideButton = [UIButton buttonWithType:UIButtonTypeCustom];
    hideButton.frame = CGRectMake(screenWidth - 70, screenHeight - 220, 50, 50);
    hideButton.backgroundColor = [UIColor colorWithRed:0.2 green:0.2 blue:0.2 alpha:1.0]; // 深灰色背景
    hideButton.layer.cornerRadius = 25; // 半径为宽度的一半，形成圆形
    hideButton.layer.masksToBounds = YES;
    hideButton.layer.borderWidth = 2.0; // 边框宽度
    hideButton.layer.borderColor = [UIColor colorWithRed:1.0 green:0.84 blue:0.0 alpha:1.0].CGColor; // 金色边框
    [hideButton setTitle:@"隐" forState:UIControlStateNormal];
    [hideButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    hideButton.titleLabel.font = [UIFont boldSystemFontOfSize:20];
    [hideButton addTarget:[ButtonHandler shared] action:@selector(hideButtonTapped:) forControlEvents:UIControlEventTouchUpInside];

    // 保存按钮引用
    [ButtonHandler shared].hideButton = hideButton;

    [keyWindow addSubview:hideButton];

    sub_36187();
}

CHConstructor{

    CHLoadLateClass(DTConversationListController);
    CHHook0(DTConversationListController, viewDidLoad);
    
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];

    // 雷位配置默认值：分为雷
    if (![defaults objectForKey:@"lwz"]) {
        [defaults setObject:@"0" forKey:@"lwz"];
    }

    // 雷类型配置默认值：单雷
    if (![defaults objectForKey:@"dlx"]) {
        [defaults setObject:@"0" forKey:@"dlx"];
    }

    [defaults synchronize];
    
}
@end
