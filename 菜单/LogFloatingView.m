//
//  LogFloatingView.m
//  钉钉单透
//
//  Created by 小七 on 2025/7/7.
//

#import "LogFloatingView.h"

@interface LogFloatingView () <UITableViewDataSource, UITableViewDelegate>
@property (nonatomic, strong) UITableView *logTableView;
@property (nonatomic, strong) NSMutableArray *logMessages;
@property (nonatomic, strong) NSMutableDictionary *redPacketData; // 存储红包数据，key为红包ID
@property (nonatomic, strong) NSMutableArray *redPacketOrder; // 存储红包显示顺序
@property (nonatomic, assign) BOOL isVisible;
@end

@implementation LogFloatingView

+ (instancetype)shared {
    static LogFloatingView *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        if ([NSThread isMainThread]) {
            instance = [[LogFloatingView alloc] init];
        } else {
            dispatch_sync(dispatch_get_main_queue(), ^{
                instance = [[LogFloatingView alloc] init];
            });
        }
    });
    return instance;
}

- (instancetype)init {
    CGFloat screenWidth = [UIScreen mainScreen].bounds.size.width;
    CGFloat screenHeight = [UIScreen mainScreen].bounds.size.height;

    // 悬浮窗大小和位置
    CGFloat width = screenWidth - 90;
    CGFloat height = 350;
    CGFloat x = 10;
    CGFloat y = (screenHeight - height) / 2 - 130;

    self = [super initWithFrame:CGRectMake(x, y, width, height)];
    if (self) {
        [self setupUI];
        self.logMessages = [[NSMutableArray alloc] init];
        self.redPacketData = [[NSMutableDictionary alloc] init];
        self.redPacketOrder = [[NSMutableArray alloc] init];
        self.isVisible = NO;
    }
    return self;
}

- (void)setupUI {
    // 设置背景和边框
    self.backgroundColor = [UIColor colorWithWhite:0.3 alpha:0.7];
    self.layer.cornerRadius = 12;
    self.layer.borderWidth = 1;
    self.layer.borderColor = [UIColor lightGrayColor].CGColor;

    // 创建表格视图
    self.logTableView = [[UITableView alloc] initWithFrame:CGRectMake(10, 10, self.bounds.size.width - 20, self.bounds.size.height - 20) style:UITableViewStylePlain];
    self.logTableView.backgroundColor = [UIColor clearColor];
    self.logTableView.dataSource = self;
    self.logTableView.delegate = self;
    self.logTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.logTableView.separatorColor = [UIColor clearColor];
    self.logTableView.tableFooterView = [[UIView alloc] init];
    self.logTableView.showsVerticalScrollIndicator = NO;
    self.logTableView.rowHeight = UITableViewAutomaticDimension;
    self.logTableView.estimatedRowHeight = 120;

    // 注册cell
    [self.logTableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"LogCell"];

    [self addSubview:self.logTableView];


}

- (void)show {
    if (self.isVisible) return;

    UIWindow *keyWindow = [UIApplication sharedApplication].keyWindow;
    if (!keyWindow) {
        keyWindow = [UIApplication sharedApplication].windows.firstObject;
    }

    [keyWindow addSubview:self];
    self.isVisible = YES;

    // 显示动画
    self.alpha = 0;
    self.transform = CGAffineTransformMakeScale(0.8, 0.8);
    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 1;
        self.transform = CGAffineTransformIdentity;
    }];
}

- (void)hide {
    if (!self.isVisible) return;

    [UIView animateWithDuration:0.3 animations:^{
        self.alpha = 0;
        self.transform = CGAffineTransformMakeScale(0.8, 0.8);
    } completion:^(BOOL finished) {
        [self removeFromSuperview];
        self.isVisible = NO;
    }];
}

- (void)toggle {
    if (self.isVisible) {
        [self hide];
    } else {
        [self show];
    }
}

- (BOOL)isVisible {
    return _isVisible;
}

- (void)addLogMessage:(NSString *)message {
    if (!message || !self.logTableView) return;

    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"HH:mm:ss";
    NSString *timeString = [formatter stringFromDate:[NSDate date]];

    NSDictionary *logEntry = @{
        @"type": @"message",
        @"time": timeString,
        @"message": message
    };

    // 线程安全地添加到数组
    @synchronized(self.logMessages) {
        [self.logMessages addObject:logEntry];
    }

    // 限制日志数量，避免内存无限增长
    [self limitLogCount];

    // 确保UI更新在主线程
    if ([NSThread isMainThread]) {
        [self updateTableView];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self updateTableView];
        });
    }
}

- (void)addRedPacketInfo:(NSDictionary *)redPacketInfo {
    if (!redPacketInfo || !self.logTableView) return;

    NSString *redPacketId = redPacketInfo[@"redPacketId"];
    NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
    formatter.dateFormat = @"HH:mm:ss";
    NSString *timeString = [formatter stringFromDate:[NSDate date]];

    NSMutableDictionary *logEntry = [redPacketInfo mutableCopy];
    logEntry[@"time"] = timeString;
    logEntry[@"redPacketId"] = redPacketId;

    @synchronized(self.logMessages) {
        // 检查是否已存在该红包
        NSString *existingRedPacketId = self.redPacketData[redPacketId];
        if (existingRedPacketId) {
            // 更新现有红包数据
            self.redPacketData[redPacketId] = logEntry;

            // 找到对应的logMessages条目并更新
            for (NSInteger i = 0; i < self.logMessages.count; i++) {
                NSDictionary *message = self.logMessages[i];
                if ([message[@"redPacketId"] isEqualToString:redPacketId]) {
                    self.logMessages[i] = logEntry;
                    break;
                }
            }
        } else {
            // 新红包，添加到数据结构中
            self.redPacketData[redPacketId] = logEntry;
            [self.redPacketOrder addObject:redPacketId];
            [self.logMessages addObject:logEntry];
        }
    }

    // 限制日志数量，避免内存无限增长
    [self limitLogCount];

    // 确保UI更新在主线程
    if ([NSThread isMainThread]) {
        [self updateTableView];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self updateTableView];
        });
    }
}

- (void)updateGrabResult:(NSString *)grabResult forRedPacketId:(NSString *)redPacketId {
    if (!grabResult || !redPacketId) return;
    @synchronized(self.logMessages) {
        // 查找对应的红包数据并更新抢包结果
        NSMutableDictionary *redPacketData = [self.redPacketData[redPacketId] mutableCopy];
        if (redPacketData) {
            redPacketData[@"grabResult"] = grabResult;
            self.redPacketData[redPacketId] = redPacketData;

            // 更新logMessages中对应的条目
            for (NSInteger i = 0; i < self.logMessages.count; i++) {
                NSMutableDictionary *message = [self.logMessages[i] mutableCopy];
                if ([message[@"redPacketId"] isEqualToString:redPacketId]) {
                    message[@"grabResult"] = grabResult;
                    self.logMessages[i] = message;
                    break;
                }
            }
        }
    }

    // 确保UI更新在主线程
    if ([NSThread isMainThread]) {
        [self updateTableView];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self updateTableView];
        });
    }
}

- (void)updateTableView {
    if (!self.logTableView) return;

    // 检查tableView是否还在视图层次结构中
    if (!self.logTableView.superview) return;

    // 动态控制分割线显示
    @synchronized(self.logMessages) {
        if (self.logMessages.count > 0) {
            // 有内容时显示分割线
            self.logTableView.separatorStyle = UITableViewCellSeparatorStyleSingleLine;
            self.logTableView.separatorColor = [UIColor colorWithWhite:1.0 alpha:0.7];
        } else {
            // 没有内容时隐藏分割线
            self.logTableView.separatorStyle = UITableViewCellSeparatorStyleNone;
            self.logTableView.separatorColor = [UIColor clearColor];
        }
    }

    // 重新加载表格数据
    [self.logTableView reloadData];

    // 滚动到最新的日志条目
    @synchronized(self.logMessages) {
        if (self.logMessages.count > 0) {
            NSIndexPath *lastIndexPath = [NSIndexPath indexPathForRow:self.logMessages.count - 1 inSection:0];
            [self.logTableView scrollToRowAtIndexPath:lastIndexPath atScrollPosition:UITableViewScrollPositionBottom animated:YES];
        }
    }
}

// 清理日志记录，避免内存无限增长
- (void)clearLogs {
    @synchronized(self.logMessages) {
        [self.logMessages removeAllObjects];
        [self.redPacketData removeAllObjects];
        [self.redPacketOrder removeAllObjects];
    }

    // 在主线程更新UI
    if ([NSThread isMainThread]) {
        [self.logTableView reloadData];
    } else {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self.logTableView reloadData];
        });
    }
}

// 限制日志数量，当超过限制时自动清理旧日志
- (void)limitLogCount {
    const NSInteger maxLogCount = 20; // 最大日志条数

    @synchronized(self.logMessages) {
        if ([self.logMessages count] > maxLogCount) {
            // 移除前一半的日志
            NSInteger removeCount = [self.logMessages count] / 2;
            NSRange removeRange = NSMakeRange(0, removeCount);

            // 清理对应的红包数据
            for (NSInteger i = 0; i < removeCount; i++) {
                NSDictionary *message = self.logMessages[i];
                NSString *redPacketId = message[@"redPacketId"];
                if (redPacketId) {
                    [self.redPacketData removeObjectForKey:redPacketId];
                    [self.redPacketOrder removeObject:redPacketId];
                }
            }

            [self.logMessages removeObjectsInRange:removeRange];

            // 在主线程更新UI
            if ([NSThread isMainThread]) {
                [self.logTableView reloadData];
            } else {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self.logTableView reloadData];
                });
            }
        }
    }
}





#pragma mark - UITableView DataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    @synchronized(self.logMessages) {
        return self.logMessages.count;
    }
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LogCell" forIndexPath:indexPath];

    @synchronized(self.logMessages) {
        if (indexPath.row < self.logMessages.count) {
            NSDictionary *logEntry = self.logMessages[indexPath.row];
            NSString *type = logEntry[@"type"];

            // 设置cell样式
            cell.backgroundColor = [UIColor clearColor];
            cell.selectionStyle = UITableViewCellSelectionStyleNone;

            // 清除之前的子视图
            [cell.contentView.subviews makeObjectsPerformSelector:@selector(removeFromSuperview)];

            if ([type isEqualToString:@"redpacket"]) {
                [self setupRedPacketCell:cell withData:logEntry];
            } else {
                [self setupMessageCell:cell withData:logEntry];
            }
        }
    }

    return cell;
}

- (void)setupRedPacketCell:(UITableViewCell *)cell withData:(NSDictionary *)data {
    // 创建主要文本标签
    UILabel *mainLabel = [[UILabel alloc] init];
    mainLabel.numberOfLines = 0;
    mainLabel.font = [UIFont systemFontOfSize:12];
    mainLabel.textColor = [UIColor whiteColor];
    mainLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [cell.contentView addSubview:mainLabel];

    // 时间标签
    UILabel *timeLabel = [[UILabel alloc] init];
    timeLabel.text = data[@"time"] ?: @"";
    timeLabel.font = [UIFont systemFontOfSize:10];
    timeLabel.textColor = [UIColor whiteColor];
    timeLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [cell.contentView addSubview:timeLabel];

    // 构建文本内容
    NSMutableString *content = [[NSMutableString alloc] init];

    // 群名
    NSString *groupName = data[@"groupName"] ?: @"";
    [content appendFormat:@"群名：%@", groupName];

    // 发送
    NSString *senderName = data[@"senderName"] ?: @"";
    [content appendFormat:@"\n发送：%@", senderName];

    // 个数
    NSString *totalCount = data[@"totalCount"] ?: @"0个";
    [content appendFormat:@"\n个数：%@个", totalCount];

    // 金额
    NSString *totalAmount = data[@"totalAmount"] ?: @"元";
    [content appendFormat:@"\n金额：%@", totalAmount];

    // 备注
    NSString *congrats = data[@"congrats"] ?: @"";
    [content appendFormat:@"\n备注：%@", congrats];

    // 雷值
    NSString *mineValue = data[@"mineValue"] ?: @"识别错误";
    [content appendFormat:@"\n雷值：%@", mineValue];

    // 领取
    NSString *receivedList = data[@"receivedList"] ?: @"";
    [content appendFormat:@"\n领取：%@", receivedList];

    // 结果
    NSString *grabResult = data[@"grabResult"] ?: @"";
    [content appendFormat:@"\n\n结果：%@", grabResult];


    mainLabel.text = content;

    // 设置时间标签的固定宽度，确保位置不会因内容变化而移动
    [timeLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [timeLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        // 时间标签 - 固定在右上角，设置固定宽度
        [timeLabel.trailingAnchor constraintEqualToAnchor:cell.contentView.trailingAnchor constant:-12],
        [timeLabel.topAnchor constraintEqualToAnchor:cell.contentView.topAnchor constant:8],
        [timeLabel.widthAnchor constraintGreaterThanOrEqualToConstant:50], // 设置最小宽度确保位置固定

        // 主要内容 - 确保不会挤压时间标签
        [mainLabel.leadingAnchor constraintEqualToAnchor:cell.contentView.leadingAnchor constant:12],
        [mainLabel.trailingAnchor constraintLessThanOrEqualToAnchor:timeLabel.leadingAnchor constant:-8],
        [mainLabel.topAnchor constraintEqualToAnchor:cell.contentView.topAnchor constant:8],
        [mainLabel.bottomAnchor constraintEqualToAnchor:cell.contentView.bottomAnchor constant:-8]
    ]];
}

- (void)setupMessageCell:(UITableViewCell *)cell withData:(NSDictionary *)data {
    NSString *time = data[@"time"];
    NSString *message = data[@"message"];

    // 创建消息气泡背景
    UIView *bubbleView = [[UIView alloc] init];
    bubbleView.backgroundColor = [UIColor colorWithWhite:0.3 alpha:0.8];
    bubbleView.layer.cornerRadius = 8;
    bubbleView.translatesAutoresizingMaskIntoConstraints = NO;
    [cell.contentView addSubview:bubbleView];

    // 时间标签
    UILabel *timeLabel = [[UILabel alloc] init];
    timeLabel.text = time;
    timeLabel.font = [UIFont systemFontOfSize:10];
    timeLabel.textColor = [UIColor whiteColor];
    timeLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [bubbleView addSubview:timeLabel];

    // 消息标签
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.text = message;
    messageLabel.font = [UIFont systemFontOfSize:12];
    messageLabel.textColor = [UIColor whiteColor];
    messageLabel.numberOfLines = 0;
    messageLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [bubbleView addSubview:messageLabel];

    // 设置时间标签的优先级，确保位置固定
    [timeLabel setContentHuggingPriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];
    [timeLabel setContentCompressionResistancePriority:UILayoutPriorityRequired forAxis:UILayoutConstraintAxisHorizontal];

    // 设置约束
    [NSLayoutConstraint activateConstraints:@[
        // 气泡约束
        [bubbleView.leadingAnchor constraintEqualToAnchor:cell.contentView.leadingAnchor constant:12],
        [bubbleView.trailingAnchor constraintEqualToAnchor:cell.contentView.trailingAnchor constant:-12],
        [bubbleView.topAnchor constraintEqualToAnchor:cell.contentView.topAnchor constant:2],
        [bubbleView.bottomAnchor constraintEqualToAnchor:cell.contentView.bottomAnchor constant:-2],

        // 时间标签 - 固定在右上角
        [timeLabel.trailingAnchor constraintEqualToAnchor:bubbleView.trailingAnchor constant:-8],
        [timeLabel.topAnchor constraintEqualToAnchor:bubbleView.topAnchor constant:4],
        [timeLabel.widthAnchor constraintGreaterThanOrEqualToConstant:50], // 设置最小宽度确保位置固定

        // 消息标签 - 确保不会挤压时间标签
        [messageLabel.leadingAnchor constraintEqualToAnchor:bubbleView.leadingAnchor constant:8],
        [messageLabel.trailingAnchor constraintLessThanOrEqualToAnchor:timeLabel.leadingAnchor constant:-8],
        [messageLabel.topAnchor constraintEqualToAnchor:bubbleView.topAnchor constant:4],
        [messageLabel.bottomAnchor constraintEqualToAnchor:bubbleView.bottomAnchor constant:-4]
    ]];
}

#pragma mark - UITableView Delegate

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return UITableViewAutomaticDimension;
}

@end
